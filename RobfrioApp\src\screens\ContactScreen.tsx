import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MapView, {Marker} from 'react-native-maps';

const ContactScreen: React.FC = () => {
  const handleWhatsApp = () => {
    const message = 'Olá! Gostaria de entrar em contato com a ROBFRIO.';
    const url = `whatsapp://send?phone=5511999999999&text=${encodeURIComponent(message)}`;
    Linking.openURL(url).catch(() => {
      Linking.openURL(`https://wa.me/5511999999999?text=${encodeURIComponent(message)}`);
    });
  };

  const handleCall = (phone: string) => {
    Linking.openURL(`tel:${phone}`);
  };

  const handleEmail = (email: string) => {
    const subject = 'Contato via App ROBFRIO';
    const body = 'O<PERSON><PERSON>,\n\nGostaria de entrar em contato com a ROBFRIO.\n\nAguardo retorno.';
    Linking.openURL(`mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  const handleDirections = () => {
    const address = 'Rua das Indústrias, 123, Distrito Industrial, São Paulo - SP';
    const url = Platform.OS === 'ios' 
      ? `maps://app?daddr=${encodeURIComponent(address)}`
      : `google.navigation:q=${encodeURIComponent(address)}`;
    
    Linking.openURL(url).catch(() => {
      // Fallback para Google Maps web
      Linking.openURL(`https://maps.google.com/?q=${encodeURIComponent(address)}`);
    });
  };

  const handleSocialMedia = (platform: string) => {
    let url = '';
    switch (platform) {
      case 'facebook':
        url = 'https://facebook.com/robfrio';
        break;
      case 'instagram':
        url = 'https://instagram.com/robfrio';
        break;
      case 'linkedin':
        url = 'https://linkedin.com/company/robfrio';
        break;
    }
    
    if (url) {
      Linking.openURL(url);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={['#1e40af', '#3b82f6']}
        style={styles.headerSection}>
        <Text style={styles.headerTitle}>Entre em Contato</Text>
        <Text style={styles.headerSubtitle}>
          Estamos prontos para atender você
        </Text>
      </LinearGradient>

      {/* Quick Actions */}
      <View style={styles.quickActionsSection}>
        <Text style={styles.sectionTitle}>Ações Rápidas</Text>
        <View style={styles.quickActionsGrid}>
          <TouchableOpacity style={styles.quickActionCard} onPress={handleWhatsApp}>
            <LinearGradient
              colors={['#25d366', '#128c7e']}
              style={styles.quickActionIcon}>
              <Icon name="chat" size={28} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.quickActionTitle}>WhatsApp</Text>
            <Text style={styles.quickActionDescription}>Resposta rápida</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.quickActionCard} 
            onPress={() => handleCall('+5511999999999')}>
            <LinearGradient
              colors={['#1e40af', '#3b82f6']}
              style={styles.quickActionIcon}>
              <Icon name="phone" size={28} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.quickActionTitle}>Ligar</Text>
            <Text style={styles.quickActionDescription}>Atendimento direto</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.quickActionCard} 
            onPress={() => handleEmail('<EMAIL>')}>
            <LinearGradient
              colors={['#f97316', '#fb923c']}
              style={styles.quickActionIcon}>
              <Icon name="email" size={28} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.quickActionTitle}>E-mail</Text>
            <Text style={styles.quickActionDescription}>Contato formal</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.quickActionCard} onPress={handleDirections}>
            <LinearGradient
              colors={['#ef4444', '#dc2626']}
              style={styles.quickActionIcon}>
              <Icon name="directions" size={28} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.quickActionTitle}>Localização</Text>
            <Text style={styles.quickActionDescription}>Como chegar</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Contact Information */}
      <View style={styles.contactInfoSection}>
        <Text style={styles.sectionTitle}>Informações de Contato</Text>
        
        <View style={styles.contactCard}>
          <LinearGradient
            colors={['#1e40af', '#3b82f6']}
            style={styles.contactIcon}>
            <Icon name="location-on" size={24} color="#ffffff" />
          </LinearGradient>
          <View style={styles.contactContent}>
            <Text style={styles.contactTitle}>Endereço</Text>
            <Text style={styles.contactDescription}>
              Rua das Indústrias, 123{'\n'}
              Distrito Industrial{'\n'}
              São Paulo - SP, 01234-567
            </Text>
          </View>
        </View>

        <View style={styles.contactCard}>
          <LinearGradient
            colors={['#25d366', '#128c7e']}
            style={styles.contactIcon}>
            <Icon name="phone" size={24} color="#ffffff" />
          </LinearGradient>
          <View style={styles.contactContent}>
            <Text style={styles.contactTitle}>Telefones</Text>
            <TouchableOpacity onPress={() => handleCall('+5511999999999')}>
              <Text style={styles.contactLink}>(11) 99999-9999</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => handleCall('+551133334444')}>
              <Text style={styles.contactLink}>(11) 3333-4444</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.contactCard}>
          <LinearGradient
            colors={['#f97316', '#fb923c']}
            style={styles.contactIcon}>
            <Icon name="email" size={24} color="#ffffff" />
          </LinearGradient>
          <View style={styles.contactContent}>
            <Text style={styles.contactTitle}>E-mails</Text>
            <TouchableOpacity onPress={() => handleEmail('<EMAIL>')}>
              <Text style={styles.contactLink}><EMAIL></Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => handleEmail('<EMAIL>')}>
              <Text style={styles.contactLink}><EMAIL></Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.contactCard}>
          <LinearGradient
            colors={['#10b981', '#34d399']}
            style={styles.contactIcon}>
            <Icon name="schedule" size={24} color="#ffffff" />
          </LinearGradient>
          <View style={styles.contactContent}>
            <Text style={styles.contactTitle}>Horário de Funcionamento</Text>
            <Text style={styles.contactDescription}>
              Segunda à Sexta: 08:00 - 18:00{'\n'}
              Sábado: 08:00 - 12:00{'\n'}
              Emergências: 24h
            </Text>
          </View>
        </View>
      </View>

      {/* Map */}
      <View style={styles.mapSection}>
        <Text style={styles.sectionTitle}>Nossa Localização</Text>
        <View style={styles.mapContainer}>
          <MapView
            style={styles.map}
            initialRegion={{
              latitude: -23.5505199,
              longitude: -46.6333824,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}>
            <Marker
              coordinate={{
                latitude: -23.5505199,
                longitude: -46.6333824,
              }}
              title="ROBFRIO"
              description="Refrigeração Comercial e Câmaras Frigoríficas"
            />
          </MapView>
          <TouchableOpacity style={styles.directionsButton} onPress={handleDirections}>
            <Icon name="directions" size={20} color="#ffffff" />
            <Text style={styles.directionsButtonText}>Como Chegar</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Social Media */}
      <View style={styles.socialSection}>
        <Text style={styles.sectionTitle}>Redes Sociais</Text>
        <View style={styles.socialButtons}>
          <TouchableOpacity 
            style={styles.socialButton}
            onPress={() => handleSocialMedia('facebook')}>
            <LinearGradient
              colors={['#1877f2', '#166fe5']}
              style={styles.socialIcon}>
              <Icon name="facebook" size={24} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.socialText}>Facebook</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.socialButton}
            onPress={() => handleSocialMedia('instagram')}>
            <LinearGradient
              colors={['#e4405f', '#c13584']}
              style={styles.socialIcon}>
              <Icon name="camera-alt" size={24} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.socialText}>Instagram</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.socialButton}
            onPress={() => handleSocialMedia('linkedin')}>
            <LinearGradient
              colors={['#0077b5', '#005885']}
              style={styles.socialIcon}>
              <Icon name="business" size={24} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.socialText}>LinkedIn</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Emergency Contact */}
      <View style={styles.emergencySection}>
        <LinearGradient
          colors={['#ef4444', '#dc2626']}
          style={styles.emergencyCard}>
          <Icon name="warning" size={32} color="#ffffff" />
          <Text style={styles.emergencyTitle}>Atendimento de Emergência</Text>
          <Text style={styles.emergencyDescription}>
            Problemas urgentes com refrigeração? Atendemos 24 horas por dia, 7 dias por semana.
          </Text>
          <TouchableOpacity 
            style={styles.emergencyButton}
            onPress={() => handleCall('+5511999999999')}>
            <Icon name="phone" size={20} color="#ef4444" />
            <Text style={styles.emergencyButtonText}>Ligar Agora</Text>
          </TouchableOpacity>
        </LinearGradient>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  headerSection: {
    paddingHorizontal: 20,
    paddingVertical: 30,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#e2e8f0',
    textAlign: 'center',
  },
  quickActionsSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  quickActionCard: {
    width: '47%',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  quickActionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 4,
  },
  quickActionDescription: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
  },
  contactInfoSection: {
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  contactCard: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  contactIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  contactContent: {
    flex: 1,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 4,
  },
  contactDescription: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
  contactLink: {
    fontSize: 14,
    color: '#1e40af',
    marginBottom: 2,
  },
  mapSection: {
    padding: 20,
  },
  mapContainer: {
    position: 'relative',
    height: 200,
    borderRadius: 12,
    overflow: 'hidden',
  },
  map: {
    flex: 1,
  },
  directionsButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    backgroundColor: '#1e40af',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 8,
  },
  directionsButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  socialSection: {
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  socialButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  socialButton: {
    alignItems: 'center',
    gap: 8,
  },
  socialIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  socialText: {
    fontSize: 12,
    color: '#64748b',
    fontWeight: '500',
  },
  emergencySection: {
    padding: 20,
  },
  emergencyCard: {
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  emergencyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    marginTop: 12,
    marginBottom: 8,
  },
  emergencyDescription: {
    fontSize: 14,
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 20,
    opacity: 0.9,
    lineHeight: 20,
  },
  emergencyButton: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  emergencyButtonText: {
    color: '#ef4444',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ContactScreen;

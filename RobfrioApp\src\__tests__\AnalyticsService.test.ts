/**
 * Testes para AnalyticsService
 * Testa funcionalidades de analytics e monitoramento
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import AnalyticsService from '../services/AnalyticsService';

// Mock do AsyncStorage
jest.mock('@react-native-async-storage/async-storage');

// Mock do DeviceInfo
jest.mock('react-native-device-info', () => ({
  getBrand: jest.fn(() => 'Apple'),
  getModel: jest.fn(() => 'iPhone 14'),
  getSystemName: jest.fn(() => 'iOS'),
  getSystemVersion: jest.fn(() => '16.0'),
  getVersion: jest.fn(() => '2.0.0'),
  getBuildNumber: jest.fn(() => '1'),
  getUniqueId: jest.fn(() => Promise.resolve('unique-device-id')),
  isTablet: jest.fn(() => false),
}));

describe('AnalyticsService', () => {
  let analyticsService: AnalyticsService;

  beforeEach(() => {
    jest.clearAllMocks();
    analyticsService = AnalyticsService.getInstance();
    
    // Mock do AsyncStorage
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
    (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('deve ser um singleton', () => {
    const instance1 = AnalyticsService.getInstance();
    const instance2 = AnalyticsService.getInstance();
    
    expect(instance1).toBe(instance2);
  });

  it('deve inicializar corretamente', async () => {
    await analyticsService.initialize();
    
    // Verificar se uma sessão foi iniciada
    expect(AsyncStorage.setItem).toHaveBeenCalled();
  });

  it('deve rastrear eventos corretamente', async () => {
    await analyticsService.initialize();
    
    await analyticsService.trackEvent('test_event', {
      property1: 'value1',
      property2: 123,
    });

    // Verificar se o evento foi salvo
    expect(AsyncStorage.setItem).toHaveBeenCalledWith(
      'analytics_events',
      expect.any(String)
    );
  });

  it('deve rastrear visualizações de tela', async () => {
    await analyticsService.initialize();
    
    await analyticsService.trackScreenView('HomeScreen');

    // Verificar se o evento de screen_view foi criado
    expect(AsyncStorage.setItem).toHaveBeenCalled();
  });

  it('deve rastrear ações do usuário', async () => {
    await analyticsService.initialize();
    
    await analyticsService.trackUserAction('button_click', {
      buttonName: 'whatsapp_contact',
      context: 'home_screen',
    });

    expect(AsyncStorage.setItem).toHaveBeenCalled();
  });

  it('deve rastrear contatos via WhatsApp', async () => {
    await analyticsService.initialize();
    
    await analyticsService.trackWhatsAppContact('home_hero_button');

    expect(AsyncStorage.setItem).toHaveBeenCalled();
  });

  it('deve rastrear ligações telefônicas', async () => {
    await analyticsService.initialize();
    
    await analyticsService.trackPhoneCall('emergency_contact');

    expect(AsyncStorage.setItem).toHaveBeenCalled();
  });

  it('deve rastrear submissões de formulário', async () => {
    await analyticsService.initialize();
    
    const formData = {
      name: 'João Silva',
      email: '<EMAIL>',
      service: 'refrigeracao',
    };

    await analyticsService.trackFormSubmission('schedule_form', formData);

    expect(AsyncStorage.setItem).toHaveBeenCalled();
  });

  it('deve rastrear erros', async () => {
    await analyticsService.initialize();
    
    const error = new Error('Teste de erro');
    await analyticsService.trackError(error, 'test_context');

    expect(AsyncStorage.setItem).toHaveBeenCalled();
  });

  it('deve rastrear métricas de performance', async () => {
    await analyticsService.initialize();
    
    await analyticsService.trackPerformance('app_load_time', 1500, 'ms');

    expect(AsyncStorage.setItem).toHaveBeenCalled();
  });

  it('deve finalizar sessão corretamente', async () => {
    await analyticsService.initialize();
    await analyticsService.endSession();

    // Verificar se a sessão foi salva
    expect(AsyncStorage.setItem).toHaveBeenCalledWith(
      'analytics_sessions',
      expect.any(String)
    );
  });

  it('deve obter eventos armazenados', async () => {
    const mockEvents = [
      {
        name: 'test_event',
        timestamp: Date.now(),
        sessionId: 'session_123',
      },
    ];

    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(
      JSON.stringify(mockEvents)
    );

    const events = await analyticsService.getStoredEvents();
    
    expect(events).toEqual(mockEvents);
    expect(AsyncStorage.getItem).toHaveBeenCalledWith('analytics_events');
  });

  it('deve obter sessões armazenadas', async () => {
    const mockSessions = [
      {
        id: 'session_123',
        startTime: Date.now(),
        endTime: Date.now() + 60000,
        screenViews: ['HomeScreen'],
        events: [],
        deviceInfo: {},
      },
    ];

    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(
      JSON.stringify(mockSessions)
    );

    const sessions = await analyticsService.getStoredSessions();
    
    expect(sessions).toEqual(mockSessions);
    expect(AsyncStorage.getItem).toHaveBeenCalledWith('analytics_sessions');
  });

  it('deve gerar relatório de analytics', async () => {
    const mockSessions = [
      {
        id: 'session_1',
        startTime: Date.now() - 60000,
        endTime: Date.now(),
        screenViews: ['HomeScreen', 'AboutScreen'],
        events: [],
        deviceInfo: {},
      },
    ];

    const mockEvents = [
      { name: 'screen_view', timestamp: Date.now(), sessionId: 'session_1' },
      { name: 'button_click', timestamp: Date.now(), sessionId: 'session_1' },
    ];

    (AsyncStorage.getItem as jest.Mock)
      .mockResolvedValueOnce(JSON.stringify(mockSessions))
      .mockResolvedValueOnce(JSON.stringify(mockEvents));

    const report = await analyticsService.getAnalyticsReport();

    expect(report.totalSessions).toBe(1);
    expect(report.totalEvents).toBe(2);
    expect(report.mostViewedScreens).toEqual([
      { screen: 'HomeScreen', count: 1 },
      { screen: 'AboutScreen', count: 1 },
    ]);
    expect(report.averageSessionDuration).toBeGreaterThan(0);
  });

  it('deve limpar dados de analytics', async () => {
    await analyticsService.clearAnalyticsData();

    expect(AsyncStorage.removeItem).toHaveBeenCalledWith('analytics_events');
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith('analytics_sessions');
  });

  it('deve lidar com erros ao obter dados armazenados', async () => {
    (AsyncStorage.getItem as jest.Mock).mockRejectedValue(
      new Error('Storage error')
    );

    const events = await analyticsService.getStoredEvents();
    const sessions = await analyticsService.getStoredSessions();

    expect(events).toEqual([]);
    expect(sessions).toEqual([]);
  });

  it('deve limitar o número de sessões armazenadas', async () => {
    // Simular 60 sessões existentes
    const existingSessions = Array.from({ length: 60 }, (_, i) => ({
      id: `session_${i}`,
      startTime: Date.now(),
      endTime: Date.now(),
      screenViews: [],
      events: [],
      deviceInfo: {},
    }));

    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(
      JSON.stringify(existingSessions)
    );

    await analyticsService.initialize();
    await analyticsService.endSession();

    // Verificar se apenas 50 sessões foram mantidas
    const savedData = (AsyncStorage.setItem as jest.Mock).mock.calls.find(
      call => call[0] === 'analytics_sessions'
    );

    if (savedData) {
      const savedSessions = JSON.parse(savedData[1]);
      expect(savedSessions.length).toBeLessThanOrEqual(50);
    }
  });
});

import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {RootStackParamList} from '../../App';

const {width} = Dimensions.get('window');
const cardWidth = (width - 60) / 2;

type ProjectsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Projects'>;

interface Project {
  id: string;
  title: string;
  description: string;
  image: any;
  category: 'all' | 'comercial' | 'camaras';
  details: string;
  temperature?: string;
  location: string;
  year: string;
}

const ProjectsScreen: React.FC = () => {
  const navigation = useNavigation<ProjectsScreenNavigationProp>();
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'comercial' | 'camaras'>('all');

  const projects: Project[] = [
    {
      id: '1',
      title: 'Supermercado ABC',
      description: 'Sistema completo de refrigeração comercial',
      image: null, // require('../assets/images/projeto1.png'),
      category: 'comercial',
      details: 'Projeto completo de refrigeração comercial para supermercado de grande porte. Incluiu instalação de sistemas de refrigeração para todas as seções, otimização energética e sistema de monitoramento automático.',
      temperature: '2°C a 6°C',
      location: 'São Paulo - SP',
      year: '2023',
    },
    {
      id: '2',
      title: 'Frigorífico XYZ',
      description: 'Câmara frigorífica de grande porte',
      image: null, // require('../assets/images/projeto2.png'),
      category: 'camaras',
      details: 'Câmara frigorífica industrial de grande porte com temperatura controlada de -25°C. Sistema completo com isolamento térmico de alta performance, controle automático de temperatura e umidade.',
      temperature: '-25°C',
      location: 'Campinas - SP',
      year: '2023',
    },
    {
      id: '3',
      title: 'Restaurante Gourmet',
      description: 'Modernização do sistema de refrigeração',
      image: null, // require('../assets/images/projeto3.png'),
      category: 'comercial',
      details: 'Modernização completa do sistema de refrigeração de restaurante gourmet. Incluiu walk-in cooler, refrigeradores reach-in, mesas refrigeradas e sistema de ventilação integrado.',
      temperature: '0°C a 8°C',
      location: 'Santos - SP',
      year: '2022',
    },
    {
      id: '4',
      title: 'Indústria Alimentícia',
      description: 'Múltiplas câmaras frigoríficas industriais',
      image: null, // require('../assets/images/projeto4.png'),
      category: 'camaras',
      details: 'Complexo industrial com 8 câmaras frigoríficas independentes para diferentes tipos de produtos. Temperaturas variando de -30°C a +8°C, sistema centralizado de controle.',
      temperature: '-30°C a +8°C',
      location: 'Guarulhos - SP',
      year: '2023',
    },
    {
      id: '5',
      title: 'Padaria Premium',
      description: 'Sistema de refrigeração para panificação',
      image: null, // require('../assets/images/projeto5.png'),
      category: 'comercial',
      details: 'Sistema especializado para padaria com diferentes zonas de temperatura para massas, produtos lácteos e produtos finalizados.',
      temperature: '4°C a 12°C',
      location: 'Osasco - SP',
      year: '2022',
    },
    {
      id: '6',
      title: 'Centro de Distribuição',
      description: 'Complexo logístico refrigerado',
      image: null, // require('../assets/images/projeto6.png'),
      category: 'camaras',
      details: 'Centro de distribuição com múltiplas câmaras para diferentes categorias de produtos, sistema automatizado de controle de temperatura.',
      temperature: '-18°C a +5°C',
      location: 'Barueri - SP',
      year: '2023',
    },
  ];

  const filteredProjects = selectedFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === selectedFilter);

  const handleProjectPress = (project: Project) => {
    navigation.navigate('ProjectDetail', {project});
  };

  const renderFilterButton = (filter: 'all' | 'comercial' | 'camaras', label: string) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        selectedFilter === filter && styles.filterButtonActive,
      ]}
      onPress={() => setSelectedFilter(filter)}>
      <Text
        style={[
          styles.filterButtonText,
          selectedFilter === filter && styles.filterButtonTextActive,
        ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={['#1e40af', '#3b82f6']}
        style={styles.headerSection}>
        <Text style={styles.headerTitle}>Galeria de Projetos</Text>
        <Text style={styles.headerSubtitle}>
          Conheça alguns dos projetos realizados pela ROBFRIO
        </Text>
      </LinearGradient>

      {/* Filters */}
      <View style={styles.filtersSection}>
        <Text style={styles.filtersTitle}>Filtrar por categoria:</Text>
        <View style={styles.filtersContainer}>
          {renderFilterButton('all', 'Todos')}
          {renderFilterButton('comercial', 'Comercial')}
          {renderFilterButton('camaras', 'Câmaras')}
        </View>
      </View>

      {/* Projects Grid */}
      <View style={styles.projectsSection}>
        <View style={styles.projectsGrid}>
          {filteredProjects.map((project) => (
            <TouchableOpacity
              key={project.id}
              style={styles.projectCard}
              onPress={() => handleProjectPress(project)}
              activeOpacity={0.8}>
              <View style={styles.projectImageContainer}>
                {project.image ? (
                  <Image source={project.image} style={styles.projectImage} />
                ) : (
                  <View style={[styles.projectImage, {backgroundColor: '#e2e8f0', justifyContent: 'center', alignItems: 'center'}]}>
                    <Icon name="image" size={40} color="#94a3b8" />
                  </View>
                )}
                <LinearGradient
                  colors={['transparent', 'rgba(0,0,0,0.7)']}
                  style={styles.projectOverlay}>
                  <View style={styles.projectInfo}>
                    <Text style={styles.projectTitle}>{project.title}</Text>
                    <Text style={styles.projectDescription}>{project.description}</Text>
                  </View>
                  <View style={styles.projectAction}>
                    <Icon name="visibility" size={20} color="#ffffff" />
                  </View>
                </LinearGradient>
              </View>
              
              <View style={styles.projectDetails}>
                <View style={styles.projectMeta}>
                  <View style={styles.metaItem}>
                    <Icon name="thermostat" size={16} color="#1e40af" />
                    <Text style={styles.metaText}>{project.temperature}</Text>
                  </View>
                  <View style={styles.metaItem}>
                    <Icon name="location-on" size={16} color="#f97316" />
                    <Text style={styles.metaText}>{project.location}</Text>
                  </View>
                </View>
                <Text style={styles.projectYear}>{project.year}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Stats Section */}
      <View style={styles.statsSection}>
        <Text style={styles.statsTitle}>Nossos Números</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <LinearGradient
              colors={['#1e40af', '#3b82f6']}
              style={styles.statBackground}>
              <Text style={styles.statNumber}>500+</Text>
              <Text style={styles.statLabel}>Projetos Realizados</Text>
            </LinearGradient>
          </View>
          
          <View style={styles.statCard}>
            <LinearGradient
              colors={['#f97316', '#fb923c']}
              style={styles.statBackground}>
              <Text style={styles.statNumber}>13+</Text>
              <Text style={styles.statLabel}>Anos de Experiência</Text>
            </LinearGradient>
          </View>
          
          <View style={styles.statCard}>
            <LinearGradient
              colors={['#10b981', '#34d399']}
              style={styles.statBackground}>
              <Text style={styles.statNumber}>100%</Text>
              <Text style={styles.statLabel}>Clientes Satisfeitos</Text>
            </LinearGradient>
          </View>
          
          <View style={styles.statCard}>
            <LinearGradient
              colors={['#8b5cf6', '#a78bfa']}
              style={styles.statBackground}>
              <Text style={styles.statNumber}>24h</Text>
              <Text style={styles.statLabel}>Suporte</Text>
            </LinearGradient>
          </View>
        </View>
      </View>

      {/* CTA Section */}
      <View style={styles.ctaSection}>
        <Text style={styles.ctaTitle}>Quer ver seu projeto aqui?</Text>
        <Text style={styles.ctaDescription}>
          Entre em contato conosco e transforme sua ideia em realidade
        </Text>
        <TouchableOpacity style={styles.ctaButton}>
          <Icon name="add" size={20} color="#ffffff" />
          <Text style={styles.ctaButtonText}>Iniciar Projeto</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  headerSection: {
    paddingHorizontal: 20,
    paddingVertical: 30,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#e2e8f0',
    textAlign: 'center',
  },
  filtersSection: {
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  filtersTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 12,
  },
  filtersContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  filterButtonActive: {
    backgroundColor: '#1e40af',
    borderColor: '#1e40af',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: '#ffffff',
  },
  projectsSection: {
    padding: 20,
  },
  projectsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  projectCard: {
    width: cardWidth,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    marginBottom: 16,
  },
  projectImageContainer: {
    position: 'relative',
    height: 150,
  },
  projectImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  projectOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '100%',
    justifyContent: 'space-between',
    padding: 12,
  },
  projectInfo: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  projectTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  projectDescription: {
    fontSize: 12,
    color: '#e2e8f0',
    lineHeight: 16,
  },
  projectAction: {
    alignSelf: 'flex-end',
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 20,
  },
  projectDetails: {
    padding: 12,
  },
  projectMeta: {
    gap: 8,
    marginBottom: 8,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    color: '#64748b',
  },
  projectYear: {
    fontSize: 12,
    color: '#94a3b8',
    textAlign: 'right',
  },
  statsSection: {
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  statsTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 20,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    width: (width - 56) / 2,
  },
  statBackground: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#ffffff',
    textAlign: 'center',
    opacity: 0.9,
  },
  ctaSection: {
    padding: 20,
    alignItems: 'center',
  },
  ctaTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 8,
    textAlign: 'center',
  },
  ctaDescription: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  ctaButton: {
    backgroundColor: '#1e40af',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 25,
    gap: 8,
  },
  ctaButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProjectsScreen;

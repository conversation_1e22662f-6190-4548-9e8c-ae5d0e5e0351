<!DOCTYPE html><html><head><meta charset="utf-8"><link rel="icon" href="data:;base64,iVBORw0KGgo="><title>React Native Remote Debugging (Deprecated)</title><script src="/debugger-ui/ui.e31bb0bc.js"></script><link rel="stylesheet" href="/debugger-ui/ui.e31bb0bc.css"></head><body> <div class="content"> <div class="warning"> <p> Remote JavaScript debugging (this workflow) is <strong>deprecated</strong> in React Native 0.73 and will be removed in a future release. </p> <p> Please use the <a href="https://reactnative.dev/docs/next/debugging#opening-the-debugger"><strong>Open Debugger</strong></a> workflow to debug apps using Hermes directly. This can be accessed from the Dev Menu, or by pressing <kbd class="shortcut">j</kbd> in the CLI (when run with <code>--experimental-debugger</code>). </p> <p> If your app is using JSC, please use <a href="https://reactnative.dev/docs/next/other-debugging-methods">direct debugging via Safari</a>. </p> <p> Learn more about debugging in React Native in the <strong><a href="https://reactnative.dev/docs/debugging">refreshed docs</a></strong> 📖. </p> </div> <label for="dark"> <input type="checkbox" id="dark" onclick="Page.toggleDarkTheme()"> Dark Theme </label> <label for="maintain-priority"> <input type="checkbox" id="maintain-priority" onclick="Page.togglePriorityMaintenance()"> Maintain Priority </label> <p> React Native JS code runs as a web worker inside this tab. </p> <p> Press <kbd id="shortcut" class="shortcut">⌘⌥I</kbd> to open Developer Tools. Enable <a href="https://stackoverflow.com/a/17324511/232122" target="_blank">Pause On Caught Exceptions</a> for a better debugging experience. </p> <p> You may also install <a href="https://www.npmjs.com/package/react-devtools" target="_blank">the standalone version of React Developer Tools</a> to inspect the React component hierarchy, their props, and state. </p> <p>Status: <span id="status">Loading...</span></p> <button class="reload-btn" onclick="window.onReloadClicked()"> Reload app </button> </div> <audio id="silence" loop="" src="/debugger-ui/priority.5583346b.wav"></audio> </body></html>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROBFRIO App - Instalação Android</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .installer-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 100%;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .title {
            font-size: 28px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 30px;
        }

        .qr-container {
            background: #f8fafc;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px dashed #1e40af;
        }

        .qr-code {
            width: 200px;
            height: 200px;
            margin: 0 auto 20px;
            background: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }

        .qr-instructions {
            font-size: 14px;
            color: #1e40af;
            font-weight: 600;
        }

        .manual-link {
            background: #1e40af;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .manual-link:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.4);
        }

        .steps {
            text-align: left;
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .step {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .step-number {
            background: #1e40af;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .feature {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            color: #0369a1;
        }

        .feature-icon {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .ip-info {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .ip-label {
            font-size: 12px;
            color: #92400e;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .ip-address {
            font-size: 18px;
            font-weight: bold;
            color: #92400e;
            font-family: monospace;
        }

        .success-message {
            background: #dcfce7;
            border: 1px solid #16a34a;
            border-radius: 8px;
            padding: 15px;
            color: #15803d;
            font-size: 14px;
            margin-bottom: 20px;
        }

        @media (max-width: 480px) {
            .installer-container {
                padding: 20px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="installer-container">
        <div class="logo">📱</div>
        <h1 class="title">ROBFRIO App v2.0</h1>
        <p class="subtitle">Instalação para Android</p>

        <div class="success-message">
            ✅ <strong>Servidor ativo!</strong> Pronto para instalação no Android
        </div>

        <div class="qr-container">
            <div class="qr-code" id="qr-code">
                📱 QR Code será gerado aqui
            </div>
            <div class="qr-instructions">
                📷 Escaneie com a câmera do Android
            </div>
        </div>

        <div class="ip-info">
            <div class="ip-label">🌐 Acesso direto:</div>
            <div class="ip-address" id="app-url">Carregando...</div>
        </div>

        <a href="#" class="manual-link" id="manual-link" target="_blank">
            🚀 Abrir Aplicativo
        </a>

        <div class="steps">
            <h3 style="margin-bottom: 15px; color: #1e40af;">📋 Como instalar:</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div>Abra o navegador no seu Android (Chrome/Firefox)</div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div>Escaneie o QR Code ou acesse o link acima</div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div>Menu do navegador > "Adicionar à tela inicial"</div>
            </div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div>Confirme a instalação</div>
            </div>
            
            <div class="step">
                <div class="step-number">5</div>
                <div>✅ Pronto! Ícone ROBFRIO na tela inicial</div>
            </div>
        </div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <div><strong>Instalação Rápida</strong><br>2 minutos</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📱</div>
                <div><strong>App Nativo</strong><br>Funciona offline</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🔄</div>
                <div><strong>Auto-Update</strong><br>Sempre atualizado</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">💾</div>
                <div><strong>Pouco Espaço</strong><br>Leve e eficiente</div>
            </div>
        </div>

        <div style="font-size: 12px; color: #64748b; margin-top: 20px;">
            💡 <strong>Dica:</strong> Certifique-se que Android e PC estão na mesma rede WiFi
        </div>
    </div>

    <script>
        // Detectar IP local e gerar URLs
        function detectLocalIP() {
            // Tentar detectar IP através de WebRTC
            const pc = new RTCPeerConnection({
                iceServers: [{urls: 'stun:stun.l.google.com:19302'}]
            });
            
            pc.createDataChannel('');
            pc.createOffer().then(offer => pc.setLocalDescription(offer));
            
            pc.onicecandidate = (ice) => {
                if (!ice || !ice.candidate || !ice.candidate.candidate) return;
                const myIP = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/.exec(ice.candidate.candidate)[1];
                if (myIP && myIP.startsWith('192.168.')) {
                    updateURLs(myIP);
                    pc.onicecandidate = () => {}; // Stop after first local IP
                }
            };
        }

        function updateURLs(ip) {
            const port = window.location.port || '8080';
            const appUrl = `http://${ip}:${port}/premium-demo.html`;
            
            document.getElementById('app-url').textContent = appUrl;
            document.getElementById('manual-link').href = appUrl;
            
            // Gerar QR Code simples (usando serviço online)
            const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(appUrl)}`;
            document.getElementById('qr-code').innerHTML = `<img src="${qrUrl}" alt="QR Code" style="width: 100%; height: 100%; object-fit: contain;">`;
        }

        // Fallback para IP atual
        function useCurrentHost() {
            const currentHost = window.location.hostname;
            const port = window.location.port || '8080';
            const appUrl = `http://${currentHost}:${port}/premium-demo.html`;
            
            document.getElementById('app-url').textContent = appUrl;
            document.getElementById('manual-link').href = appUrl;
            
            const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(appUrl)}`;
            document.getElementById('qr-code').innerHTML = `<img src="${qrUrl}" alt="QR Code" style="width: 100%; height: 100%; object-fit: contain;">`;
        }

        // Inicializar
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            detectLocalIP();
            setTimeout(useCurrentHost, 3000); // Fallback após 3 segundos
        } else {
            useCurrentHost();
        }

        // Verificar se servidor está ativo
        fetch('/premium-demo.html')
            .then(response => {
                if (response.ok) {
                    console.log('✅ Servidor ativo e aplicativo disponível');
                } else {
                    console.log('⚠️ Aplicativo pode não estar disponível');
                }
            })
            .catch(error => {
                console.log('❌ Erro ao verificar servidor:', error);
            });
    </script>
</body>
</html>

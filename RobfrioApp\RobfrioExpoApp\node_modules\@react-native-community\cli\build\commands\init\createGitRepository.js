"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function _cliTools() {
  const data = require("@react-native-community/cli-tools");
  _cliTools = function () {
    return data;
  };
  return data;
}
function _execa() {
  const data = _interopRequireDefault(require("execa"));
  _execa = function () {
    return data;
  };
  return data;
}
function _fs() {
  const data = _interopRequireDefault(require("fs"));
  _fs = function () {
    return data;
  };
  return data;
}
function _path() {
  const data = _interopRequireDefault(require("path"));
  _path = function () {
    return data;
  };
  return data;
}
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const createGitRepository = async folder => {
  const loader = (0, _cliTools().getLoader)();
  try {
    await (0, _execa().default)('git', ['--version'], {
      stdio: 'ignore'
    });
  } catch {
    loader.fail('Unable to initialize Git repo. `git` not in $PATH.');
    return;
  }
  try {
    await (0, _execa().default)('git', ['rev-parse', '--is-inside-work-tree'], {
      stdio: 'ignore',
      cwd: folder
    });
    loader.succeed('New project is already inside of a Git repo, skipping git init.');
    return;
  } catch {}
  loader.start('Initializing Git repository');
  let version;
  try {
    version = JSON.parse(_fs().default.readFileSync(_path().default.join('node_modules/react-native/package.json'), 'utf8')).version;
  } catch {}
  try {
    await (0, _execa().default)('git', ['init'], {
      cwd: folder
    });
    await (0, _execa().default)('git', ['branch', '-M', 'main'], {
      cwd: folder
    });
    await (0, _execa().default)('git', ['add', '.'], {
      cwd: folder
    });
    await (0, _execa().default)('git', ['commit', '-m', `Initial commit\n\n${version ? 'Generated by react-native@' + version : ''}`], {
      cwd: folder
    });
    loader.succeed();
  } catch (e) {
    loader.fail('Could not create an empty Git repository, see debug logs with --verbose');
    _cliTools().logger.debug(e);
  }
};
var _default = createGitRepository;
exports.default = _default;

//# sourceMappingURL=createGitRepository.ts.map
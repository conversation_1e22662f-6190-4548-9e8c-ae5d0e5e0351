import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
  Dimensions,
  RefreshControl,
  Platform,
} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Services
import AnalyticsService from '../services/AnalyticsService';
import NotificationService from '../services/NotificationService';

const {width: screenWidth} = Dimensions.get('window');

interface QuickStats {
  projectsCompleted: number;
  clientsSatisfied: number;
  yearsExperience: number;
  emergencyResponse: string;
}

interface WeatherInfo {
  temperature: number;
  condition: string;
  recommendation: string;
}

const HomeScreen: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [quickStats, setQuickStats] = useState<QuickStats>({
    projectsCompleted: 500,
    clientsSatisfied: 450,
    yearsExperience: 13,
    emergencyResponse: '24h',
  });
  const [weatherInfo, setWeatherInfo] = useState<WeatherInfo>({
    temperature: 25,
    condition: 'Ideal para refrigeração',
    recommendation: 'Ótimo momento para manutenção preventiva',
  });
  const [lastVisit, setLastVisit] = useState<string>('');

  const analytics = AnalyticsService.getInstance();
  const notifications = NotificationService.getInstance();

  useFocusEffect(
    useCallback(() => {
      analytics.trackScreenView('HomeScreen');
      loadUserData();
    }, []),
  );

  useEffect(() => {
    initializeServices();
  }, []);

  const initializeServices = async () => {
    try {
      await analytics.initialize();
      await notifications.initialize();

      // Agendar lembrete de manutenção se for um novo usuário
      const isFirstTime = await AsyncStorage.getItem('isFirstTimeUser');
      if (!isFirstTime) {
        await notifications.scheduleMaintenanceReminder('Sistema de Refrigeração', 30);
        await AsyncStorage.setItem('isFirstTimeUser', 'false');
      }
    } catch (error) {
      console.error('Erro ao inicializar serviços:', error);
    }
  };

  const loadUserData = async () => {
    try {
      const lastVisitData = await AsyncStorage.getItem('lastVisit');
      if (lastVisitData) {
        const lastDate = new Date(lastVisitData);
        setLastVisit(lastDate.toLocaleDateString('pt-BR'));
      }

      // Salvar visita atual
      await AsyncStorage.setItem('lastVisit', new Date().toISOString());

      // Simular carregamento de estatísticas atualizadas
      await updateQuickStats();
    } catch (error) {
      console.error('Erro ao carregar dados do usuário:', error);
    }
  };

  const updateQuickStats = async () => {
    // Simular atualização de estatísticas em tempo real
    const randomIncrement = Math.floor(Math.random() * 5);
    setQuickStats(prev => ({
      ...prev,
      projectsCompleted: prev.projectsCompleted + randomIncrement,
    }));
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await analytics.trackUserAction('pull_to_refresh', {screen: 'HomeScreen'});

    try {
      await updateQuickStats();
      // Simular carregamento de dados
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Erro ao atualizar dados:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  const handleWhatsApp = async () => {
    await analytics.trackWhatsAppContact('home_hero_button');

    const phoneNumber = '5511999999999';
    const message = 'Olá! Gostaria de solicitar um orçamento para refrigeração comercial através do aplicativo ROBFRIO.';
    const url = `whatsapp://send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;

    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert(
          'WhatsApp não encontrado',
          'O WhatsApp não está instalado neste dispositivo. Deseja instalar?',
          [
            {text: 'Cancelar', style: 'cancel'},
            {
              text: 'Instalar',
              onPress: () => Linking.openURL('https://whatsapp.com/download'),
            },
          ],
        );
      }
    } catch (error) {
      console.error('Erro ao abrir WhatsApp:', error);
      await analytics.trackError(error as Error, 'whatsapp_open_failed');
    }
  };

  const handleCall = async () => {
    await analytics.trackPhoneCall('home_hero_button');

    const phoneNumber = 'tel:+5511999999999';
    try {
      await Linking.openURL(phoneNumber);
    } catch (error) {
      console.error('Erro ao fazer ligação:', error);
      await analytics.trackError(error as Error, 'phone_call_failed');
    }
  };

  const handleEmail = async () => {
    await analytics.trackUserAction('email_contact', {context: 'home_hero'});

    const email = 'mailto:<EMAIL>?subject=Solicitação de Orçamento via App&body=Olá! Gostaria de solicitar um orçamento através do aplicativo ROBFRIO.';
    try {
      await Linking.openURL(email);
    } catch (error) {
      console.error('Erro ao abrir email:', error);
      await analytics.trackError(error as Error, 'email_open_failed');
    }
  };

  const handleEmergencyContact = async () => {
    await analytics.trackUserAction('emergency_contact', {context: 'home_screen'});

    Alert.alert(
      '🚨 Atendimento de Emergência',
      'Você está prestes a entrar em contato com nosso atendimento de emergência 24h. Este serviço é para situações urgentes de refrigeração.',
      [
        {text: 'Cancelar', style: 'cancel'},
        {
          text: 'Ligar Agora',
          style: 'destructive',
          onPress: () => Linking.openURL('tel:+5511999999999'),
        },
      ],
    );
  };

  const handleTestNotification = async () => {
    await analytics.trackUserAction('test_notification', {context: 'home_screen'});
    await notifications.testNotification();
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Welcome Back Section */}
      {lastVisit && (
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>
            Bem-vindo de volta! Última visita: {lastVisit}
          </Text>
        </View>
      )}

      {/* Weather Info Section */}
      <View style={styles.weatherSection}>
        <Text style={styles.weatherTitle}>🌡️ Condições Ideais</Text>
        <Text style={styles.weatherTemp}>{weatherInfo.temperature}°C</Text>
        <Text style={styles.weatherCondition}>{weatherInfo.condition}</Text>
        <Text style={styles.weatherRecommendation}>{weatherInfo.recommendation}</Text>
      </View>

      {/* Hero Section */}
      <View style={styles.heroSection}>
        <View style={styles.heroContent}>
          <Text style={styles.heroTitle}>
            Soluções Completas em{'\n'}
            <Text style={styles.heroAccent}>Refrigeração Comercial</Text>
          </Text>
          <Text style={styles.heroDescription}>
            Há mais de 13 anos no mercado, a ROBFRIO oferece serviços especializados em
            refrigeração comercial e montagem de câmaras frigoríficas.
          </Text>
          <View style={styles.heroButtons}>
            <TouchableOpacity style={styles.primaryButton} onPress={handleWhatsApp}>
              <Text style={styles.primaryButtonText}>📱 Solicitar Orçamento</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.secondaryButton} onPress={handleCall}>
              <Text style={styles.secondaryButtonText}>📞 Ligar Agora</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Enhanced Stats Section */}
      <View style={styles.statsSection}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{quickStats.yearsExperience}+</Text>
          <Text style={styles.statLabel}>Anos de Experiência</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{quickStats.projectsCompleted}+</Text>
          <Text style={styles.statLabel}>Projetos Realizados</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{quickStats.clientsSatisfied}+</Text>
          <Text style={styles.statLabel}>Clientes Satisfeitos</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{quickStats.emergencyResponse}</Text>
          <Text style={styles.statLabel}>Atendimento</Text>
        </View>
      </View>

      {/* Services Preview */}
      <View style={styles.servicesPreview}>
        <Text style={styles.sectionTitle}>Nossos Serviços</Text>
        <View style={styles.serviceCards}>
          <View style={styles.serviceCard}>
            <View style={styles.serviceIcon}>
              <Text style={styles.serviceEmoji}>❄️</Text>
            </View>
            <Text style={styles.serviceTitle}>Refrigeração Comercial</Text>
            <Text style={styles.serviceDescription}>
              Instalação, manutenção e modernização de sistemas completos
            </Text>
          </View>
          
          <View style={styles.serviceCard}>
            <View style={[styles.serviceIcon, {backgroundColor: '#f97316'}]}>
              <Text style={styles.serviceEmoji}>🏪</Text>
            </View>
            <Text style={styles.serviceTitle}>Câmaras Frigoríficas</Text>
            <Text style={styles.serviceDescription}>
              Projeto personalizado, montagem e sistemas de controle
            </Text>
          </View>
        </View>
      </View>

      {/* Enhanced Quick Actions */}
      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>Ações Rápidas</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={handleWhatsApp}>
            <Text style={styles.actionEmoji}>💬</Text>
            <Text style={styles.actionText}>WhatsApp</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleCall}>
            <Text style={styles.actionEmoji}>📞</Text>
            <Text style={styles.actionText}>Ligar</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleEmail}>
            <Text style={styles.actionEmoji}>📧</Text>
            <Text style={styles.actionText}>E-mail</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleEmergencyContact}>
            <Text style={styles.actionEmoji}>🚨</Text>
            <Text style={styles.actionText}>Emergência</Text>
          </TouchableOpacity>
        </View>

        {/* Additional Actions Row */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={handleTestNotification}>
            <Text style={styles.actionEmoji}>🔔</Text>
            <Text style={styles.actionText}>Teste Notif.</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={() => analytics.trackUserAction('location_view')}>
            <Text style={styles.actionEmoji}>📍</Text>
            <Text style={styles.actionText}>Localização</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={() => analytics.trackUserAction('share_app')}>
            <Text style={styles.actionEmoji}>📤</Text>
            <Text style={styles.actionText}>Compartilhar</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={() => analytics.trackUserAction('feedback')}>
            <Text style={styles.actionEmoji}>⭐</Text>
            <Text style={styles.actionText}>Avaliar</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Promotions Section */}
      <View style={styles.promotionsSection}>
        <Text style={styles.sectionTitle}>🎉 Ofertas Especiais</Text>
        <View style={styles.promotionCard}>
          <Text style={styles.promotionTitle}>Manutenção Preventiva</Text>
          <Text style={styles.promotionDiscount}>15% OFF</Text>
          <Text style={styles.promotionDescription}>
            Desconto especial em manutenções preventivas. Mantenha seu sistema funcionando perfeitamente!
          </Text>
          <TouchableOpacity style={styles.promotionButton} onPress={handleWhatsApp}>
            <Text style={styles.promotionButtonText}>Aproveitar Oferta</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Company Info */}
      <View style={styles.companyInfo}>
        <Text style={styles.sectionTitle}>ROBFRIO</Text>
        <Text style={styles.companyDescription}>
          Especialista em refrigeração comercial e câmaras frigoríficas desde 2011.
          Oferecemos soluções completas para seu negócio com qualidade e confiança.
        </Text>
        <Text style={styles.appVersion}>
          App versão 2.0.0 - Desenvolvido com ❄️ e ☕
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  welcomeSection: {
    backgroundColor: '#e0f2fe',
    padding: 12,
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 14,
    color: '#0369a1',
    fontWeight: '500',
  },
  weatherSection: {
    backgroundColor: '#ffffff',
    margin: 16,
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  weatherTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 8,
  },
  weatherTemp: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#059669',
    marginBottom: 4,
  },
  weatherCondition: {
    fontSize: 16,
    color: '#059669',
    marginBottom: 8,
  },
  weatherRecommendation: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  heroSection: {
    backgroundColor: '#1e40af',
    marginHorizontal: 16,
    paddingHorizontal: 20,
    paddingVertical: 40,
    borderRadius: 20,
    marginBottom: 20,
  },
  heroContent: {
    alignItems: 'center',
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 36,
  },
  heroAccent: {
    color: '#fbbf24',
  },
  heroDescription: {
    fontSize: 16,
    color: '#e2e8f0',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
    paddingHorizontal: 10,
  },
  heroButtons: {
    flexDirection: 'row',
    gap: 12,
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: '#f97316',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    shadowColor: '#f97316',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  secondaryButtonText: {
    color: '#1e40af',
    fontSize: 16,
    fontWeight: '600',
  },
  statsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    paddingHorizontal: 16,
    backgroundColor: '#ffffff',
    marginHorizontal: 16,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statCard: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 11,
    color: '#64748b',
    textAlign: 'center',
    fontWeight: '500',
  },
  servicesPreview: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 20,
    textAlign: 'center',
  },
  serviceCards: {
    flexDirection: 'row',
    gap: 16,
  },
  serviceCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  serviceIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#1e40af',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  serviceEmoji: {
    fontSize: 30,
  },
  serviceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 8,
    textAlign: 'center',
  },
  serviceDescription: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 20,
  },
  quickActions: {
    padding: 20,
    backgroundColor: '#ffffff',
    marginHorizontal: 16,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  actionButton: {
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    minWidth: 70,
    flex: 1,
    maxWidth: (screenWidth - 80) / 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  actionEmoji: {
    fontSize: 20,
    marginBottom: 6,
  },
  actionText: {
    fontSize: 10,
    color: '#64748b',
    fontWeight: '500',
    textAlign: 'center',
  },
  promotionsSection: {
    padding: 20,
    backgroundColor: '#ffffff',
    marginHorizontal: 16,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  promotionCard: {
    backgroundColor: '#fef3c7',
    padding: 20,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#f59e0b',
  },
  promotionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#92400e',
    marginBottom: 8,
  },
  promotionDiscount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#dc2626',
    marginBottom: 8,
  },
  promotionDescription: {
    fontSize: 14,
    color: '#78350f',
    marginBottom: 16,
    lineHeight: 20,
  },
  promotionButton: {
    backgroundColor: '#f59e0b',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  promotionButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  companyInfo: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: '#ffffff',
    marginHorizontal: 16,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  companyDescription: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 12,
  },
  appVersion: {
    fontSize: 12,
    color: '#94a3b8',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default HomeScreen;

"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "AndroidDependencyConfig", {
  enumerable: true,
  get: function () {
    return _android.AndroidDependencyConfig;
  }
});
Object.defineProperty(exports, "AndroidDependencyParams", {
  enumerable: true,
  get: function () {
    return _android.AndroidDependencyParams;
  }
});
Object.defineProperty(exports, "AndroidProjectConfig", {
  enumerable: true,
  get: function () {
    return _android.AndroidProjectConfig;
  }
});
Object.defineProperty(exports, "AndroidProjectParams", {
  enumerable: true,
  get: function () {
    return _android.AndroidProjectParams;
  }
});
Object.defineProperty(exports, "IOSDependencyConfig", {
  enumerable: true,
  get: function () {
    return _ios.IOSDependencyConfig;
  }
});
Object.defineProperty(exports, "IOSDependencyParams", {
  enumerable: true,
  get: function () {
    return _ios.IOSDependencyParams;
  }
});
Object.defineProperty(exports, "IOSProjectConfig", {
  enumerable: true,
  get: function () {
    return _ios.IOSProjectConfig;
  }
});
Object.defineProperty(exports, "IOSProjectInfo", {
  enumerable: true,
  get: function () {
    return _ios.IOSProjectInfo;
  }
});
Object.defineProperty(exports, "IOSProjectParams", {
  enumerable: true,
  get: function () {
    return _ios.IOSProjectParams;
  }
});
var _ios = require("./ios");
var _android = require("./android");

//# sourceMappingURL=index.ts.map
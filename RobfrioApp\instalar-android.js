#!/usr/bin/env node

/**
 * 📱 INSTALADOR ANDROID - ROBFRIO APP v2.0
 * Script automatizado para instalação no Android
 */

const { execSync } = require('child_process');
const fs = require('fs');
const os = require('os');

console.log('\n📱 INSTALADOR ANDROID - ROBFRIO APP v2.0\n');

// Função para obter IP local
function getLocalIP() {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
        for (const interface of interfaces[name]) {
            if (interface.family === 'IPv4' && !interface.internal) {
                return interface.address;
            }
        }
    }
    return 'localhost';
}

// Função para verificar se comando existe
function commandExists(command) {
    try {
        execSync(`${command} --version`, { stdio: 'ignore' });
        return true;
    } catch (error) {
        return false;
    }
}

// Função para verificar porta disponível
function isPortAvailable(port) {
    try {
        const result = execSync(`netstat -an | grep :${port}`, { encoding: 'utf8', stdio: 'pipe' });
        return result.trim() === '';
    } catch (error) {
        return true; // Se netstat falhar, assumir que porta está disponível
    }
}

console.log('🔍 ANALISANDO OPÇÕES DE INSTALAÇÃO...\n');

const localIP = getLocalIP();
const hasNode = commandExists('node');
const hasJava = commandExists('java');
const hasAdb = commandExists('adb');
const hasAndroidHome = process.env.ANDROID_HOME || process.env.ANDROID_SDK_ROOT;

console.log('📊 STATUS DO AMBIENTE:');
console.log(`   Node.js: ${hasNode ? '✅' : '❌'}`);
console.log(`   Java: ${hasJava ? '✅' : '❌'}`);
console.log(`   Android SDK: ${hasAndroidHome ? '✅' : '❌'}`);
console.log(`   ADB: ${hasAdb ? '✅' : '❌'}`);
console.log(`   IP Local: ${localIP}`);
console.log('');

// Determinar melhor método de instalação
let recommendedMethod = '';
let canBuildNative = hasNode && hasJava && hasAndroidHome && hasAdb;
let canRunPWA = hasNode;

if (canBuildNative) {
    recommendedMethod = 'NATIVO';
    console.log('🎯 MÉTODO RECOMENDADO: INSTALAÇÃO NATIVA');
    console.log('   ✅ Ambiente completo detectado');
    console.log('   ✅ Pode gerar APK nativo');
    console.log('');
} else if (canRunPWA) {
    recommendedMethod = 'PWA';
    console.log('🎯 MÉTODO RECOMENDADO: PWA (Progressive Web App)');
    console.log('   ✅ Node.js disponível');
    console.log('   ✅ Instalação mais rápida e confiável');
    console.log('');
} else {
    recommendedMethod = 'MANUAL';
    console.log('🎯 MÉTODO RECOMENDADO: INSTALAÇÃO MANUAL');
    console.log('   ⚠️  Ambiente precisa ser configurado');
    console.log('');
}

// Opções de instalação
console.log('📱 OPÇÕES DISPONÍVEIS:\n');

console.log('1️⃣  PWA - PROGRESSIVE WEB APP (RECOMENDADO)');
console.log('   ⏱️  Tempo: 2 minutos');
console.log('   🎯 Dificuldade: Fácil');
console.log('   ✅ Funciona como app nativo');
console.log('   ✅ Todas as funcionalidades');
console.log('   ✅ Não precisa configurar ambiente');
console.log('');

console.log('2️⃣  APLICATIVO NATIVO');
console.log('   ⏱️  Tempo: 30 minutos');
console.log('   🎯 Dificuldade: Avançada');
console.log('   ✅ APK nativo Android');
console.log('   ⚠️  Requer Android Studio');
console.log('');

console.log('3️⃣  INSTALAÇÃO MANUAL');
console.log('   ⏱️  Tempo: 5 minutos');
console.log('   🎯 Dificuldade: Média');
console.log('   ✅ APK pré-compilado');
console.log('   ⚠️  Requer transferir arquivo');
console.log('');

// Executar método recomendado
console.log('🚀 INICIANDO INSTALAÇÃO...\n');

if (recommendedMethod === 'PWA') {
    console.log('📱 CONFIGURANDO PWA (PROGRESSIVE WEB APP):\n');
    
    try {
        // Verificar se servidor já está rodando
        const port = 8080;
        if (!isPortAvailable(port)) {
            console.log(`✅ Servidor já rodando na porta ${port}`);
        } else {
            console.log('🌐 Iniciando servidor web...');
            
            // Iniciar servidor em background
            const { spawn } = require('child_process');
            const serverProcess = spawn('python', ['-m', 'http.server', '8080'], {
                cwd: './web-demo',
                detached: true,
                stdio: 'ignore'
            });
            
            serverProcess.unref();
            console.log('✅ Servidor iniciado com sucesso!');
        }
        
        console.log('\n📱 INSTRUÇÕES PARA INSTALAR NO ANDROID:\n');
        console.log('1️⃣  Abra o navegador no seu Android (Chrome/Firefox)');
        console.log(`2️⃣  Acesse: http://${localIP}:8080/premium-demo.html`);
        console.log('3️⃣  Menu do navegador > "Adicionar à tela inicial"');
        console.log('4️⃣  Confirme a instalação');
        console.log('5️⃣  Pronto! Ícone ROBFRIO na tela inicial');
        
        console.log('\n🔗 LINKS DIRETOS:');
        console.log(`   📱 Android: http://${localIP}:8080/premium-demo.html`);
        console.log(`   💻 Desktop: http://localhost:8080/premium-demo.html`);
        
        console.log('\n💡 DICAS:');
        console.log('   ✅ Certifique-se que Android e PC estão na mesma rede WiFi');
        console.log('   ✅ Se não funcionar, use o IP localhost no PC');
        console.log('   ✅ PWA funciona offline após primeira instalação');
        
    } catch (error) {
        console.log('❌ Erro ao iniciar servidor:', error.message);
        console.log('\n🔧 SOLUÇÃO ALTERNATIVA:');
        console.log('   1. cd web-demo');
        console.log('   2. python -m http.server 8080');
        console.log(`   3. Acessar: http://${localIP}:8080/premium-demo.html`);
    }
    
} else if (recommendedMethod === 'NATIVO') {
    console.log('📱 CONFIGURANDO APLICATIVO NATIVO:\n');
    
    try {
        console.log('🔧 Verificando dependências...');
        execSync('npm install', { stdio: 'inherit' });
        
        console.log('📱 Verificando dispositivos Android...');
        const devices = execSync('adb devices', { encoding: 'utf8' });
        console.log(devices);
        
        if (devices.includes('device')) {
            console.log('✅ Dispositivo Android detectado!');
            console.log('🚀 Iniciando instalação...');
            
            // Executar build para Android
            execSync('npm run android', { stdio: 'inherit' });
            
            console.log('\n🎉 INSTALAÇÃO CONCLUÍDA!');
            console.log('✅ Aplicativo ROBFRIO instalado no seu Android');
            
        } else {
            console.log('⚠️  Nenhum dispositivo Android detectado');
            console.log('\n📱 CONECTAR DISPOSITIVO:');
            console.log('   1. Habilitar "Opções do desenvolvedor"');
            console.log('   2. Ativar "Depuração USB"');
            console.log('   3. Conectar via USB');
            console.log('   4. Executar novamente: node instalar-android.js');
        }
        
    } catch (error) {
        console.log('❌ Erro na instalação nativa:', error.message);
        console.log('\n🔄 ALTERNATIVA: Usar PWA');
        console.log('   Execute novamente e escolha opção PWA');
    }
    
} else {
    console.log('📋 INSTALAÇÃO MANUAL NECESSÁRIA:\n');
    
    console.log('🔧 CONFIGURAR AMBIENTE:');
    console.log('   1. Instalar Android Studio');
    console.log('   2. Configurar ANDROID_HOME');
    console.log('   3. Instalar Java JDK 11+');
    console.log('   4. Executar: node instalar-android.js');
    
    console.log('\n🚀 ALTERNATIVA RÁPIDA - PWA:');
    console.log('   1. Instalar Node.js');
    console.log('   2. Executar: node instalar-android.js');
    console.log('   3. Seguir instruções PWA');
}

console.log('\n📚 DOCUMENTAÇÃO COMPLETA:');
console.log('   📄 INSTALACAO_RAPIDA.md - Guia detalhado');
console.log('   📄 INSTALACAO_ANDROID.md - Configuração completa');

console.log('\n🆘 SUPORTE:');
console.log('   💬 PWA é a opção mais confiável');
console.log('   📱 Funciona em qualquer Android moderno');
console.log('   ⚡ Instalação em 2 minutos');

console.log('\n🎯 INSTALAÇÃO INICIADA COM SUCESSO!\n');

// Salvar configurações
const config = {
    timestamp: new Date().toISOString(),
    method: recommendedMethod,
    localIP,
    environment: {
        hasNode,
        hasJava,
        hasAdb,
        hasAndroidHome: !!hasAndroidHome
    },
    urls: {
        android: `http://${localIP}:8080/premium-demo.html`,
        desktop: 'http://localhost:8080/premium-demo.html'
    }
};

fs.writeFileSync('instalacao-config.json', JSON.stringify(config, null, 2));
console.log('📄 Configuração salva em: instalacao-config.json');

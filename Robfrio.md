# Prompt para IA - Desenvolvimento Site ROBFRIO

## Contexto da Empresa
A ROBFRIO é uma empresa especializada em refrigeração comercial e montagem de câmaras frigoríficas, atuando no mercado desde 2011. A empresa busca modernizar sua presença digital para atrair mais clientes, melhorar a comunicação e otimizar o controle de agendamentos.

## Objetivos do Site
1. **Atração de Clientes**: Interface moderna e profissional que transmita confiança
2. **Comunicação Eficaz**: Canais diretos de contato (WhatsApp, formulários, telefone)
3. **Controle de Agendamentos**: Sistema simples para agendar visitas e orçamentos
4. **Showcase de Serviços**: Apresentação clara dos serviços oferecidos

## Especificações Técnicas

### Tecnologias a Usar:
- HTML5 semântico
- CSS3 com Flexbox/Grid
- JavaScript vanilla (ES6+)
- Responsivo (Mobile-first)
- Animações CSS/JS suaves
- Formulários funcionais

### Estrutura do Site:

#### 1. Header/Navegação
- Logo da ROBFRIO
- Menu hambúrguer para mobile
- Navegação sticky
- Links para seções principais

#### 2. Hero Section
- Título impactante sobre refrigeração comercial
- Subtítulo mencionando os 13+ anos de experiência (desde 2011)
- CTA principal "Solicitar Orçamento"
- Imagem/vídeo de fundo relacionada à refrigeração

#### 3. Seção Sobre
- Breve história da empresa (desde 2011)
- Missão, visão, valores
- Diferencial competitivo
- Números de projetos realizados

#### 4. Serviços Principais
Cards para cada serviço:
- **Refrigeração Comercial**
  - Instalação de sistemas
  - Manutenção preventiva/corretiva
  - Modernização de equipamentos
- **Câmaras Frigoríficas**
  - Projeto personalizado
  - Montagem completa
  - Isolamento térmico
  - Sistemas de controle

#### 5. Galeria de Projetos
- Grid responsivo com projetos realizados
- Modal para visualização ampliada
- Filtros por tipo de projeto

#### 6. Sistema de Agendamento
- Formulário com campos:
  - Nome/Empresa
  - Telefone/WhatsApp
  - Email
  - Tipo de serviço
  - Data preferencial
  - Descrição do projeto
- Validação de campos
- Confirmação visual do envio

#### 7. Contato
- Informações da empresa
- Mapa integrado
- Botões de ação rápida (WhatsApp, Telefone)
- Formulário de contato secundário

#### 8. Footer
- Informações de contato
- Links para redes sociais
- Certificações/Licenças
- Copyright

### Design Guidelines:

#### Paleta de Cores:
- Azul profissional (#1e40af, #3b82f6)
- Branco (#ffffff)
- Cinza moderno (#64748b, #334155)
- Accent laranja (#f97316) para CTAs

#### Tipografia:
- Headings: Font moderna (Inter, Poppins)
- Body: Legível (System fonts ou Google Fonts)
- Hierarquia clara de tamanhos

#### Elementos Visuais:
- Ícones relacionados à refrigeração
- Gradientes sutis
- Sombras suaves
- Bordas arredondadas
- Animações de entrada (fade-in, slide-up)

### Funcionalidades Específicas:

#### Sistema de Agendamento:
```javascript
// Estrutura do objeto de agendamento
const agendamento = {
  nome: '',
  empresa: '',
  telefone: '',
  email: '',
  tipoServico: '',
  dataPreferencial: '',
  descricao: '',
  timestamp: new Date()
}
```

#### Integração WhatsApp:
- Botão flutuante de WhatsApp
- Links diretos com mensagem pré-formatada
- Integração nos formulários

#### Validações:
- Campos obrigatórios
- Formato de email
- Formato de telefone brasileiro
- Datas futuras para agendamento

### Performance e SEO:
- Otimização de imagens
- Meta tags apropriadas
- Schema markup para negócios locais
- Loading lazy para imagens
- Minificação de CSS/JS

### Responsividade:
- Breakpoints: 320px, 768px, 1024px, 1440px
- Menu mobile com animação
- Cards que se adaptam ao layout
- Textos legíveis em todos os tamanhos

## Prompt para a IA:

"Desenvolva um site completo e moderno para a empresa ROBFRIO, especializada em refrigeração comercial e montagem de câmaras frigoríficas desde 2011. O site deve ser responsivo, profissional e incluir:

1. Design moderno com paleta azul/branco/laranja
2. Sistema de agendamento funcional
3. Integração WhatsApp
4. Galeria de projetos
5. Formulários de contato
6. Animações suaves
7. SEO otimizado

Crie um arquivo HTML único com CSS e JavaScript integrados, priorizando performance e experiência do usuário. O site deve transmitir confiança e profissionalismo, destacando a experiência de 13+ anos da empresa."

## Estrutura de Arquivos Recomendada:
```
robfrio-site/
├── index.html
├── assets/
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── script.js
│   └── images/
│       ├── logo.png
│       ├── hero-bg.jpg
│       └── projetos/
└── README.md
```

## Checklist de Entrega:
- [ ] Layout responsivo testado
- [ ] Formulários funcionais
- [ ] Integração WhatsApp ativa
- [ ] Animações implementadas
- [ ] SEO básico configurado
- [ ] Performance otimizada
- [ ] Cross-browser compatibility
- [ ] Accessibility básico (WCAG)
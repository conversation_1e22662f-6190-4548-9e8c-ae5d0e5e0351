import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  Linking,
  Platform,
} from 'react-native';
// import {Picker} from '@react-native-picker/picker';
// import DateTimePicker from '@react-native-community/datetimepicker';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface FormData {
  nome: string;
  empresa: string;
  telefone: string;
  email: string;
  tipoServico: string;
  dataPreferencial: Date | null;
  descricao: string;
}

const ScheduleScreen: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    nome: '',
    empresa: '',
    telefone: '',
    email: '',
    tipoServico: '',
    dataPreferencial: null,
    descricao: '',
  });
  
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^[\(\)\s\-\+\d]{10,}$/;
    return phoneRegex.test(phone);
  };

  const formatPhone = (text: string): string => {
    const numbers = text.replace(/\D/g, '');
    if (numbers.length <= 11) {
      if (numbers.length <= 10) {
        return numbers.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
      } else {
        return numbers.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
      }
    }
    return text;
  };

  const handlePhoneChange = (text: string) => {
    const formatted = formatPhone(text);
    setFormData({...formData, telefone: formatted});
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setFormData({...formData, dataPreferencial: selectedDate});
    }
  };

  const validateForm = (): boolean => {
    if (!formData.nome.trim()) {
      Alert.alert('Erro', 'Por favor, preencha o nome.');
      return false;
    }
    
    if (!formData.telefone.trim()) {
      Alert.alert('Erro', 'Por favor, preencha o telefone.');
      return false;
    }
    
    if (!validatePhone(formData.telefone)) {
      Alert.alert('Erro', 'Por favor, insira um telefone válido.');
      return false;
    }
    
    if (!formData.email.trim()) {
      Alert.alert('Erro', 'Por favor, preencha o e-mail.');
      return false;
    }
    
    if (!validateEmail(formData.email)) {
      Alert.alert('Erro', 'Por favor, insira um e-mail válido.');
      return false;
    }
    
    if (!formData.tipoServico) {
      Alert.alert('Erro', 'Por favor, selecione o tipo de serviço.');
      return false;
    }
    
    if (formData.dataPreferencial) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (formData.dataPreferencial < today) {
        Alert.alert('Erro', 'Por favor, selecione uma data futura.');
        return false;
      }
    }
    
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setIsLoading(true);
    
    // Simular envio
    setTimeout(() => {
      setIsLoading(false);
      
      Alert.alert(
        'Sucesso!',
        'Solicitação enviada com sucesso! Entraremos em contato em breve.',
        [
          {
            text: 'OK',
            onPress: () => {
              // Limpar formulário
              setFormData({
                nome: '',
                empresa: '',
                telefone: '',
                email: '',
                tipoServico: '',
                dataPreferencial: null,
                descricao: '',
              });
              
              // Enviar para WhatsApp
              sendToWhatsApp();
            },
          },
        ]
      );
    }, 2000);
  };

  const sendToWhatsApp = () => {
    const message = `
*Nova Solicitação de Orçamento - ROBFRIO*

*Nome:* ${formData.nome}
*Empresa:* ${formData.empresa || 'Não informado'}
*Telefone:* ${formData.telefone}
*E-mail:* ${formData.email}
*Serviço:* ${formData.tipoServico}
*Data Preferencial:* ${formData.dataPreferencial ? formData.dataPreferencial.toLocaleDateString('pt-BR') : 'Não informado'}
*Descrição:* ${formData.descricao || 'Não informado'}

_Enviado via App ROBFRIO em: ${new Date().toLocaleString('pt-BR')}_
    `.trim();
    
    const url = `whatsapp://send?phone=5511999999999&text=${encodeURIComponent(message)}`;
    
    Alert.alert(
      'Enviar via WhatsApp?',
      'Deseja enviar esta solicitação também via WhatsApp?',
      [
        {text: 'Não', style: 'cancel'},
        {
          text: 'Sim',
          onPress: () => {
            Linking.openURL(url).catch(() => {
              Linking.openURL(`https://wa.me/5511999999999?text=${encodeURIComponent(message)}`);
            });
          },
        },
      ]
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={['#1e40af', '#3b82f6']}
        style={styles.headerSection}>
        <Text style={styles.headerTitle}>Solicitar Orçamento</Text>
        <Text style={styles.headerSubtitle}>
          Preencha o formulário e receba um orçamento personalizado
        </Text>
      </LinearGradient>

      {/* Benefits */}
      <View style={styles.benefitsSection}>
        <Text style={styles.benefitsTitle}>Por que escolher a ROBFRIO?</Text>
        <View style={styles.benefitsList}>
          <View style={styles.benefitItem}>
            <Icon name="check-circle" size={20} color="#10b981" />
            <Text style={styles.benefitText}>Orçamento gratuito e sem compromisso</Text>
          </View>
          <View style={styles.benefitItem}>
            <Icon name="check-circle" size={20} color="#10b981" />
            <Text style={styles.benefitText}>Visita técnica especializada</Text>
          </View>
          <View style={styles.benefitItem}>
            <Icon name="check-circle" size={20} color="#10b981" />
            <Text style={styles.benefitText}>Projeto personalizado para sua necessidade</Text>
          </View>
          <View style={styles.benefitItem}>
            <Icon name="check-circle" size={20} color="#10b981" />
            <Text style={styles.benefitText}>Garantia de qualidade e durabilidade</Text>
          </View>
          <View style={styles.benefitItem}>
            <Icon name="check-circle" size={20} color="#10b981" />
            <Text style={styles.benefitText}>Suporte técnico pós-instalação</Text>
          </View>
        </View>
      </View>

      {/* Form */}
      <View style={styles.formSection}>
        <Text style={styles.formTitle}>Dados para Contato</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Nome Completo *</Text>
          <TextInput
            style={styles.textInput}
            value={formData.nome}
            onChangeText={(text) => setFormData({...formData, nome: text})}
            placeholder="Digite seu nome completo"
            placeholderTextColor="#94a3b8"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Empresa</Text>
          <TextInput
            style={styles.textInput}
            value={formData.empresa}
            onChangeText={(text) => setFormData({...formData, empresa: text})}
            placeholder="Nome da empresa (opcional)"
            placeholderTextColor="#94a3b8"
          />
        </View>

        <View style={styles.inputRow}>
          <View style={[styles.inputGroup, {flex: 1, marginRight: 8}]}>
            <Text style={styles.inputLabel}>Telefone/WhatsApp *</Text>
            <TextInput
              style={styles.textInput}
              value={formData.telefone}
              onChangeText={handlePhoneChange}
              placeholder="(11) 99999-9999"
              placeholderTextColor="#94a3b8"
              keyboardType="phone-pad"
            />
          </View>
          
          <View style={[styles.inputGroup, {flex: 1, marginLeft: 8}]}>
            <Text style={styles.inputLabel}>E-mail *</Text>
            <TextInput
              style={styles.textInput}
              value={formData.email}
              onChangeText={(text) => setFormData({...formData, email: text})}
              placeholder="<EMAIL>"
              placeholderTextColor="#94a3b8"
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
        </View>

        <View style={styles.inputRow}>
          <View style={[styles.inputGroup, {flex: 1, marginRight: 8}]}>
            <Text style={styles.inputLabel}>Tipo de Serviço *</Text>
            <View style={styles.pickerContainer}>
              <TextInput
                style={styles.textInput}
                value={formData.tipoServico}
                onChangeText={(text) => setFormData({...formData, tipoServico: text})}
                placeholder="Digite o tipo de serviço"
                placeholderTextColor="#94a3b8"
              />
            </View>
          </View>
          
          <View style={[styles.inputGroup, {flex: 1, marginLeft: 8}]}>
            <Text style={styles.inputLabel}>Data Preferencial</Text>
            <TouchableOpacity
              style={styles.dateButton}
              onPress={() => setShowDatePicker(true)}>
              <Text style={[styles.dateButtonText, !formData.dataPreferencial && {color: '#94a3b8'}]}>
                {formData.dataPreferencial 
                  ? formData.dataPreferencial.toLocaleDateString('pt-BR')
                  : 'Selecionar data'
                }
              </Text>
              <Icon name="event" size={20} color="#64748b" />
            </TouchableOpacity>
          </View>
        </View>

        {/* DatePicker será implementado quando as dependências estiverem instaladas */}

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Descrição do Projeto</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            value={formData.descricao}
            onChangeText={(text) => setFormData({...formData, descricao: text})}
            placeholder="Descreva brevemente seu projeto ou necessidade..."
            placeholderTextColor="#94a3b8"
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        <TouchableOpacity
          style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={isLoading}>
          {isLoading ? (
            <Text style={styles.submitButtonText}>Enviando...</Text>
          ) : (
            <>
              <Icon name="send" size={20} color="#ffffff" />
              <Text style={styles.submitButtonText}>Enviar Solicitação</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Contact Info */}
      <View style={styles.contactSection}>
        <Text style={styles.contactTitle}>Ou entre em contato diretamente:</Text>
        <View style={styles.contactButtons}>
          <TouchableOpacity style={styles.contactButton}>
            <Icon name="phone" size={24} color="#1e40af" />
            <Text style={styles.contactButtonText}>(11) 99999-9999</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.contactButton}>
            <Icon name="email" size={24} color="#f97316" />
            <Text style={styles.contactButtonText}><EMAIL></Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  headerSection: {
    paddingHorizontal: 20,
    paddingVertical: 30,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#e2e8f0',
    textAlign: 'center',
  },
  benefitsSection: {
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  benefitsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 16,
  },
  benefitsList: {
    gap: 12,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  benefitText: {
    fontSize: 14,
    color: '#64748b',
    flex: 1,
  },
  formSection: {
    padding: 20,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#374151',
    backgroundColor: '#ffffff',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    backgroundColor: '#ffffff',
  },
  picker: {
    height: 50,
    color: '#374151',
  },
  dateButton: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  dateButtonText: {
    fontSize: 16,
    color: '#374151',
  },
  submitButton: {
    backgroundColor: '#1e40af',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
    marginTop: 8,
  },
  submitButtonDisabled: {
    backgroundColor: '#94a3b8',
  },
  submitButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  contactSection: {
    padding: 20,
    backgroundColor: '#f8fafc',
    alignItems: 'center',
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 16,
  },
  contactButtons: {
    gap: 12,
    width: '100%',
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    gap: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  contactButtonText: {
    fontSize: 16,
    color: '#64748b',
    fontWeight: '500',
  },
});

export default ScheduleScreen;

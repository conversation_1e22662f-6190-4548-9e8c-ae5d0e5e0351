var e=require("graphql"),t=require("./d354e5b3.min.js"),r=require("wonka");function n(e,t){if(Array.isArray(e))for(var r=0;r<e.length;r++)n(e[r],t);else if("object"==typeof e&&null!==e)for(var o in e)"__typename"===o&&"string"==typeof e[o]?t[e[o]]=0:n(e[o],t);return t}function o(e){return Object.keys(n(e,{}))}var i=function(r){if(r.selectionSet&&!r.selectionSet.selections.some((function(t){return t.kind===e.Kind.FIELD&&"__typename"===t.name.value&&!t.alias})))return t._extends({},r,{selectionSet:t._extends({},r.selectionSet,{selections:r.selectionSet.selections.concat([{kind:e.Kind.FIELD,name:{kind:e.Kind.NAME,value:"__typename"}}])})})},a=new Map;function u(r){var n=t.keyDocument(r),o=a.get(n.__key);return o||(o=e.visit(n,{Field:i,InlineFragment:i}),Object.defineProperty(o,"__key",{value:n.__key,enumerable:!1}),a.set(n.__key,o)),o}function s(e){return e&&"object"==typeof e?Object.keys(e).reduce((function(t,r){var n=e[r];return"__typename"===r?Object.defineProperty(t,"__typename",{enumerable:!1,value:n}):t[r]=Array.isArray(n)?n.map(s):n&&"object"==typeof n&&"__typename"in n?s(n):n,t}),Array.isArray(e)?[]:{}):e}function c(e){return e.toPromise=function(){return r.toPromise(r.take(1)(r.filter((function(e){return!e.stale&&!e.hasNext}))(e)))},e}function f(e,t,r){return r||(r=t.context),{key:t.key,query:t.query,variables:t.variables,kind:e,context:r}}function p(e,r){return f(e.kind,e,t._extends({},e.context,{meta:t._extends({},e.context.meta,r)}))}function l(){}function d(r,n,o){for(var i=0;i<o.length;i++)if(o[i].kind===e.Kind.FRAGMENT_DEFINITION){var a=o[i].name.value,u=t.stringifyDocument(o[i]);r.has(a)||(r.set(a,u),n.push(o[i]))}else n.push(o[i])}function y(e){var t=e.kind;return"mutation"!==t&&"query"!==t}function k(e){var n=e.forward,i=e.client,a=new Map,s=Object.create(null);function c(e){var t=f(e.kind,e);return t.query=u(e.query),t}function l(e){var t=e.context.requestPolicy;return"query"===e.kind&&"network-only"!==t&&("cache-only"===t||a.has(e.key))}return function(e){var u=r.share(e),f=r.map((function(e){var r=a.get(e.key),n=t._extends({},r,{operation:p(e,{cacheOutcome:r?"hit":"miss"})});return"cache-and-network"===e.context.requestPolicy&&(n.stale=!0,x(i,e)),n}))(r.filter((function(e){return!y(e)&&l(e)}))(u)),d=r.tap((function(e){var t=e.operation;if(t){var r=o(e.data).concat(t.context.additionalTypenames||[]);if("mutation"===e.operation.kind){for(var n=new Set,u=0;u<r.length;u++){var c=r[u],f=s[c]||(s[c]=new Set);f.forEach((function(e){n.add(e)})),f.clear()}n.forEach((function(e){a.has(e)&&(t=a.get(e).operation,a.delete(e),x(i,t))}))}else if("query"===t.kind&&e.data){a.set(t.key,e);for(var p=0;p<r.length;p++){var l=r[p];(s[l]||(s[l]=new Set)).add(t.key)}}}}))(n(r.filter((function(e){return"query"!==e.kind||"cache-only"!==e.context.requestPolicy}))(r.map((function(e){return p(e,{cacheOutcome:"miss"})}))(r.merge([r.map(c)(r.filter((function(e){return!y(e)&&!l(e)}))(u)),r.filter((function(e){return y(e)}))(u)])))));return r.merge([f,d])}}function x(e,r){return e.reexecuteOperation(f(r.kind,r,t._extends({},r.context,{requestPolicy:"network-only"})))}var h=new Set;function m(e){var t=e.forward,n=new Set;function o(e){var t=e.key,r=e.kind;if("teardown"===r)return n.delete(t),!0;if("query"!==r&&"subscription"!==r)return!0;var o=n.has(t);return n.add(t),!o}function i(e){e.hasNext||n.delete(e.operation.key)}return function(e){var n=r.filter(o)(e);return r.tap(i)(t(n))}}function v(e){var n=e.forward;return function(e){var o=r.share(e),i=r.mergeMap((function(e){var n=e.key,i=r.filter((function(e){return"teardown"===e.kind&&e.key===n}))(o),a=t.makeFetchBody(e),u=t.makeFetchURL(e,a),s=t.makeFetchOptions(e,a);return r.onPush((function(e){}))(r.takeUntil(i)(t.makeFetchSource(e,u,s)))}))(r.filter((function(e){return"query"===e.kind||"mutation"===e.kind}))(o)),a=n(r.filter((function(e){return"query"!==e.kind&&"mutation"!==e.kind}))(o));return r.merge([i,a])}}function g(e){return function(e){return r.filter((function(){return!1}))(r.tap((function(e){}))(e))}}var b=g();function q(e){return function(t){var r=t.client;return e.reduceRight((function(e,t){return t({client:r,forward:e,dispatchDebug:function(e){}})}),t.forward)}}var w=[m,k,v],E=function e(n){var o=new Map,i=new Map,a=[],u=r.makeSubject(),p=u.source,d=u.next,y=!1;function k(e){for(y=!0,e&&d(e);e=a.shift();)d(e);y=!1}function x(e){var n=r.filter((function(t){return t.operation.kind===e.kind&&t.operation.key===e.key}))(E);return m.maskTypename&&(n=r.map((function(e){return t._extends({},e,{data:s(e.data)})}))(n)),"mutation"===e.kind?r.take(1)(r.onStart((function(){return k(e)}))(n)):r.share(r.onEnd((function(){o.delete(e.key),i.delete(e.key);for(var t=a.length-1;t>=0;t--)a[t].key===e.key&&a.splice(t,1);k(f("teardown",e,e.context))}))(r.onPush((function(t){o.set(e.key,t)}))(r.switchMap((function(n){return"query"!==e.kind||n.stale?r.fromValue(n):r.merge([r.fromValue(n),r.map((function(){return t._extends({},n,{stale:!0})}))(r.take(1)(r.filter((function(t){return"query"===t.kind&&t.key===e.key&&"cache-only"!==t.context.requestPolicy}))(p)))])}))(r.takeUntil(r.filter((function(t){return"teardown"===t.kind&&t.key===e.key}))(p))(n)))))}var h=this instanceof e?this:Object.create(e.prototype),m=t._extends(h,{url:n.url,fetchOptions:n.fetchOptions,fetch:n.fetch,suspense:!!n.suspense,requestPolicy:n.requestPolicy||"cache-first",preferGetMethod:!!n.preferGetMethod,maskTypename:!!n.maskTypename,operations$:p,reexecuteOperation:function(e){("mutation"===e.kind||i.has(e.key))&&(a.push(e),y||Promise.resolve().then(k))},createOperationContext:function(e){return e||(e={}),t._extends({},{url:m.url,fetchOptions:m.fetchOptions,fetch:m.fetch,preferGetMethod:m.preferGetMethod},e,{suspense:e.suspense||!1!==e.suspense&&m.suspense,requestPolicy:e.requestPolicy||m.requestPolicy})},createRequestOperation:function(e,r,n){return t.getOperationType(r.query),f(e,r,m.createOperationContext(n))},executeRequestOperation:function(e){return"mutation"===e.kind?x(e):r.make((function(n){var a=i.get(e.key);a||i.set(e.key,a=x(e));var u="cache-and-network"===e.context.requestPolicy||"network-only"===e.context.requestPolicy;return r.subscribe(n.next)(r.onEnd(n.complete)(r.onStart((function(){var r=o.get(e.key);if("subscription"===e.kind)return k(e);u&&k(e),null!=r&&r===o.get(e.key)?n.next(u?t._extends({},r,{stale:!0}):r):u||k(e)}))(a))).unsubscribe}))},executeQuery:function(e,t){var r=m.createRequestOperation("query",e,t);return m.executeRequestOperation(r)},executeSubscription:function(e,t){var r=m.createRequestOperation("subscription",e,t);return m.executeRequestOperation(r)},executeMutation:function(e,t){var r=m.createRequestOperation("mutation",e,t);return m.executeRequestOperation(r)},query:function(e,r,n){return n&&"boolean"==typeof n.suspense||(n=t._extends({},n,{suspense:!1})),c(m.executeQuery(t.createRequest(e,r),n))},readQuery:function(e,t,n){var o=null;return r.subscribe((function(e){o=e}))(m.query(e,t,n)).unsubscribe(),o},subscription:function(e,r,n){return m.executeSubscription(t.createRequest(e,r),n)},mutation:function(e,r,n){return c(m.executeMutation(t.createRequest(e,r),n))}}),v=l,b=q(void 0!==n.exchanges?n.exchanges:w),E=r.share(b({client:m,dispatchDebug:v,forward:g()})(p));return r.publish(E),m},O=E;exports.CombinedError=t.CombinedError,exports.createRequest=t.createRequest,exports.getOperationName=t.getOperationName,exports.makeErrorResult=t.makeErrorResult,exports.makeResult=t.makeResult,exports.mergeResultPatch=t.mergeResultPatch,exports.stringifyVariables=t.stringifyVariables,exports.Client=E,exports.cacheExchange=k,exports.composeExchanges=q,exports.createClient=O,exports.debugExchange=function(e){var t=e.forward;return function(e){return t(e)}},exports.dedupExchange=m,exports.defaultExchanges=w,exports.errorExchange=function(e){var t=e.onError;return function(e){var n=e.forward;return function(e){return r.tap((function(e){var r=e.error;r&&t(r,e.operation)}))(n(e))}}},exports.fallbackExchangeIO=b,exports.fetchExchange=v,exports.formatDocument=u,exports.gql=function(){for(var r=arguments,n=new Map,o=[],i=[],a=Array.isArray(arguments[0])?arguments[0][0]:arguments[0]||"",u=1;u<arguments.length;u++){var s=r[u];s&&s.definitions?i.push.apply(i,s.definitions):a+=s,a+=r[0][u]}return d(n,o,t.keyDocument(a).definitions),d(n,o,i),t.keyDocument({kind:e.Kind.DOCUMENT,definitions:o})},exports.makeOperation=f,exports.maskTypename=s,exports.ssrExchange=function(e){var n=!(!e||!e.staleWhileRevalidate),o=!(!e||!e.includeExtensions),i={},a=[];function u(e){a.push(e.operation.key),1===a.length&&Promise.resolve().then((function(){for(var e;e=a.shift();)i[e]=null}))}var s=function(a){var s=a.client,c=a.forward;return function(a){var f=e&&"boolean"==typeof e.isClient?!!e.isClient:!s.suspense,p=r.share(a),l=c(r.filter((function(e){return!i[e.key]||!!i[e.key].hasNext}))(p)),d=r.map((function(e){var r=function(e,r,n){return{operation:e,data:r.data?JSON.parse(r.data):void 0,extensions:n&&r.extensions?JSON.parse(r.extensions):void 0,error:r.error?new t.CombinedError({networkError:r.error.networkError?new Error(r.error.networkError):void 0,graphQLErrors:r.error.graphQLErrors}):void 0,hasNext:r.hasNext}}(e,i[e.key],o);return n&&!h.has(e.key)&&(r.stale=!0,h.add(e.key),x(s,e)),r}))(r.filter((function(e){return!!i[e.key]}))(p));return f?d=r.tap(u)(d):l=r.tap((function(e){var t=e.operation;if("mutation"!==t.kind){var r=function(e,t){var r=e.hasNext,n=e.data,o=e.extensions,i=e.error,a={};return void 0!==n&&(a.data=JSON.stringify(n)),t&&void 0!==o&&(a.extensions=JSON.stringify(o)),r&&(a.hasNext=!0),i&&(a.error={graphQLErrors:i.graphQLErrors.map((function(e){return e.path||e.extensions?{message:e.message,path:e.path,extensions:e.extensions}:e.message}))},i.networkError&&(a.error.networkError=""+i.networkError)),a}(e,o);i[t.key]=r}}))(l),r.merge([l,d])}};return s.restoreData=function(e){for(var t in e)null!==i[t]&&(i[t]=e[t])},s.extractData=function(){var e={};for(var t in i)null!=i[t]&&(e[t]=i[t]);return e},e&&e.initialState&&s.restoreData(e.initialState),s},exports.subscriptionExchange=function(n){var o=n.forwardSubscription,i=n.enableAllOperations;return function(n){var a=n.client,u=n.forward;function s(e){var t=e.kind;return"subscription"===t||!!i&&("query"===t||"mutation"===t)}return function(n){var i=r.share(n),c=r.mergeMap((function(n){var u=n.key,s=r.filter((function(e){return"teardown"===e.kind&&e.key===u}))(i);return r.takeUntil(s)(function(n){var i=o({key:n.key.toString(36),query:e.print(n.query),variables:n.variables,context:t._extends({},n.context)});return r.make((function(e){var r,o=e.next,u=e.complete,s=!1;return Promise.resolve().then((function(){s||(r=i.subscribe({next:function(e){return o(t.makeResult(n,e))},error:function(e){return o(t.makeErrorResult(n,e))},complete:function(){s||(s=!0,"subscription"===n.kind&&a.reexecuteOperation(f("teardown",n,n.context)),u())}}))})),function(){s=!0,r&&r.unsubscribe()}}))}(n))}))(r.filter(s)(i)),p=u(r.filter((function(e){return!s(e)}))(i));return r.merge([c,p])}}};
//# sourceMappingURL=urql-core.min.js.map

<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="16" cy="16" r="15" fill="url(#faviconGradient)" stroke="#f97316" stroke-width="1"/>
  
  <!-- Snowflake Icon -->
  <g transform="translate(16,16)" fill="white">
    <!-- Main cross -->
    <rect x="-1" y="-8" width="2" height="16"/>
    <rect x="-8" y="-1" width="16" height="2"/>
    
    <!-- Diagonal lines -->
    <rect x="-1" y="-8" width="2" height="16" transform="rotate(45)"/>
    <rect x="-1" y="-8" width="2" height="16" transform="rotate(-45)"/>
    
    <!-- Decorative ends -->
    <circle cx="0" cy="-7" r="1.5"/>
    <circle cx="0" cy="7" r="1.5"/>
    <circle cx="-7" cy="0" r="1.5"/>
    <circle cx="7" cy="0" r="1.5"/>
  </g>
</svg>

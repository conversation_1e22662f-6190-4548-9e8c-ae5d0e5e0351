<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROBFRIO App - Demonstração</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .phone-container {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: #1e40af;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .app-header {
            height: 60px;
            background: #1e40af;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        .app-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            padding: 30px 20px;
            text-align: center;
            border-radius: 20px;
            margin-bottom: 20px;
        }
        
        .hero-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .hero-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .cta-button {
            background: #f97316;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1e40af;
        }
        
        .stat-label {
            font-size: 12px;
            color: #64748b;
            margin-top: 5px;
        }
        
        .service-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .service-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 8px;
        }
        
        .service-description {
            font-size: 14px;
            color: #64748b;
            line-height: 1.5;
        }
        
        .bottom-tabs {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
        }
        
        .tab-item.active {
            color: #1e40af;
        }
        
        .tab-item:not(.active) {
            color: #64748b;
        }
        
        .tab-icon {
            width: 24px;
            height: 24px;
            background: currentColor;
            border-radius: 4px;
        }
        
        .tab-label {
            font-size: 10px;
            font-weight: 500;
        }
        
        .demo-info {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #1e40af;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            z-index: 1000;
        }
        
        .whatsapp-float {
            position: absolute;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #25d366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #64748b;
        }
    </style>
</head>
<body>
    <div class="demo-info">📱 ROBFRIO App - Demonstração Interativa</div>
    
    <div class="phone-container">
        <div class="phone-screen">
            <div class="status-bar">
                <span>9:41</span>
                <span>ROBFRIO</span>
                <span>100%</span>
            </div>
            
            <div class="app-header">
                <span id="header-title">ROBFRIO</span>
            </div>
            
            <div class="app-content" id="app-content">
                <!-- Home Screen Content -->
                <div id="home-screen">
                    <div class="hero-section">
                        <div class="hero-title">Soluções Completas em<br>Refrigeração Comercial</div>
                        <div class="hero-subtitle">Há mais de 13 anos no mercado</div>
                        <button class="cta-button" onclick="openWhatsApp()">📱 Solicitar Orçamento</button>
                        <button class="cta-button" onclick="callPhone()">📞 Ligar Agora</button>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">13+</div>
                            <div class="stat-label">Anos</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">Projetos</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">Satisfação</div>
                        </div>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-title">❄️ Refrigeração Comercial</div>
                        <div class="service-description">Instalação, manutenção e modernização de sistemas completos</div>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-title">🏪 Câmaras Frigoríficas</div>
                        <div class="service-description">Projeto personalizado, montagem e sistemas de controle</div>
                    </div>
                </div>
                
                <!-- Other screens will be loaded here -->
                <div id="other-screens" style="display: none;">
                    <div class="loading">
                        <h3>Tela Carregando...</h3>
                        <p>Esta demonstração mostra a tela Home.<br>O app completo tem 7 telas funcionais!</p>
                    </div>
                </div>
            </div>
            
            <div class="whatsapp-float" onclick="openWhatsApp()">
                💬
            </div>
            
            <div class="bottom-tabs">
                <div class="tab-item active" onclick="switchTab('home', 'ROBFRIO')">
                    <div class="tab-icon"></div>
                    <div class="tab-label">Início</div>
                </div>
                <div class="tab-item" onclick="switchTab('about', 'Sobre Nós')">
                    <div class="tab-icon"></div>
                    <div class="tab-label">Sobre</div>
                </div>
                <div class="tab-item" onclick="switchTab('services', 'Serviços')">
                    <div class="tab-icon"></div>
                    <div class="tab-label">Serviços</div>
                </div>
                <div class="tab-item" onclick="switchTab('projects', 'Projetos')">
                    <div class="tab-icon"></div>
                    <div class="tab-label">Projetos</div>
                </div>
                <div class="tab-item" onclick="switchTab('schedule', 'Agendar')">
                    <div class="tab-icon"></div>
                    <div class="tab-label">Agendar</div>
                </div>
                <div class="tab-item" onclick="switchTab('contact', 'Contato')">
                    <div class="tab-icon"></div>
                    <div class="tab-label">Contato</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function switchTab(tab, title) {
            // Update header
            document.getElementById('header-title').textContent = title;
            
            // Update active tab
            document.querySelectorAll('.tab-item').forEach(item => item.classList.remove('active'));
            event.currentTarget.classList.add('active');
            
            // Show content
            if (tab === 'home') {
                document.getElementById('home-screen').style.display = 'block';
                document.getElementById('other-screens').style.display = 'none';
            } else {
                document.getElementById('home-screen').style.display = 'none';
                document.getElementById('other-screens').style.display = 'block';
            }
        }
        
        function openWhatsApp() {
            alert('📱 WhatsApp Integration\n\nNo app real, isso abriria:\nwhatsapp://send?phone=5511999999999&text=Olá! Gostaria de solicitar um orçamento.');
        }
        
        function callPhone() {
            alert('📞 Phone Integration\n\nNo app real, isso faria uma ligação para:\n(11) 99999-9999');
        }
        
        // Simulate app loading
        setTimeout(() => {
            console.log('📱 ROBFRIO App Demo carregado com sucesso!');
        }, 1000);
    </script>
</body>
</html>

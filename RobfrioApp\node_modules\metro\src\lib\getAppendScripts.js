/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

"use strict";

var _CountingSet = _interopRequireDefault(require("./CountingSet"));
function _interopRequireDefault(obj) {
  return obj && obj.__esModule
    ? obj
    : {
        default: obj,
      };
}
const getInlineSourceMappingURL = require("../DeltaBundler/Serializers/helpers/getInlineSourceMappingURL");
const sourceMapString = require("../DeltaBundler/Serializers/sourceMapString");
const countLines = require("./countLines");
const nullthrows = require("nullthrows");
function getAppendScripts(entryPoint, modules, options) {
  const output = [];
  if (options.runModule) {
    const paths = [...options.runBeforeMainModule, entryPoint];
    for (const path of paths) {
      if (modules.some((module) => module.path === path)) {
        const code = options.getRunModuleStatement(
          options.createModuleId(path)
        );
        output.push({
          path: `require-${path}`,
          dependencies: new Map(),
          getSource: () => Buffer.from(""),
          inverseDependencies: new _CountingSet.default(),
          output: [
            {
              type: "js/script/virtual",
              data: {
                code,
                lineCount: countLines(code),
                map: [],
              },
            },
          ],
        });
      }
    }
  }
  if (options.inlineSourceMap || options.sourceMapUrl) {
    const sourceMappingURL = options.inlineSourceMap
      ? getInlineSourceMappingURL(
          sourceMapString(modules, {
            processModuleFilter: () => true,
            excludeSource: false,
            shouldAddToIgnoreList: options.shouldAddToIgnoreList,
          })
        )
      : nullthrows(options.sourceMapUrl);
    const code = `//# sourceMappingURL=${sourceMappingURL}`;
    output.push({
      path: "source-map",
      dependencies: new Map(),
      getSource: () => Buffer.from(""),
      inverseDependencies: new _CountingSet.default(),
      output: [
        {
          type: "js/script/virtual",
          data: {
            code,
            lineCount: countLines(code),
            map: [],
          },
        },
      ],
    });
  }
  if (options.sourceUrl) {
    const code = `//# sourceURL=${options.sourceUrl}`;
    output.push({
      path: "source-url",
      dependencies: new Map(),
      getSource: () => Buffer.from(""),
      inverseDependencies: new _CountingSet.default(),
      output: [
        {
          type: "js/script/virtual",
          data: {
            code,
            lineCount: countLines(code),
            map: [],
          },
        },
      ],
    });
  }
  return output;
}
module.exports = getAppendScripts;

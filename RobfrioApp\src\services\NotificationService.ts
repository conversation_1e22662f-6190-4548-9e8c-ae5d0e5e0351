/**
 * Serviço de Notificações Push
 * Gerencia notificações locais e push notifications
 */

import {Alert, Platform} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface NotificationData {
  id: string;
  title: string;
  body: string;
  data?: any;
  scheduledDate?: Date;
  type: 'maintenance' | 'promotion' | 'reminder' | 'emergency';
}

class NotificationService {
  private static instance: NotificationService;
  private isInitialized = false;

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  async initialize(): Promise<boolean> {
    try {
      if (this.isInitialized) return true;

      // Solicitar permissões de notificação
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        console.warn('Permissões de notificação negadas');
        return false;
      }

      // Configurar handlers de notificação
      this.setupNotificationHandlers();

      // Agendar notificações padrão
      await this.scheduleDefaultNotifications();

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Erro ao inicializar NotificationService:', error);
      return false;
    }
  }

  private async requestPermissions(): Promise<boolean> {
    try {
      // Simular solicitação de permissões
      // Em um app real, usaria react-native-permissions ou similar
      return true;
    } catch (error) {
      console.error('Erro ao solicitar permissões:', error);
      return false;
    }
  }

  private setupNotificationHandlers(): void {
    // Configurar handlers para quando o app está em foreground/background
    console.log('Notification handlers configurados');
  }

  async scheduleNotification(notification: NotificationData): Promise<boolean> {
    try {
      // Salvar notificação no AsyncStorage para persistência
      const notifications = await this.getStoredNotifications();
      notifications.push(notification);
      await AsyncStorage.setItem('notifications', JSON.stringify(notifications));

      // Em um app real, agendaria a notificação usando uma biblioteca como
      // @react-native-async-storage/async-storage ou react-native-push-notification
      console.log('Notificação agendada:', notification.title);
      return true;
    } catch (error) {
      console.error('Erro ao agendar notificação:', error);
      return false;
    }
  }

  async scheduleMaintenanceReminder(
    equipmentType: string,
    daysFromNow: number,
  ): Promise<void> {
    const scheduledDate = new Date();
    scheduledDate.setDate(scheduledDate.getDate() + daysFromNow);

    const notification: NotificationData = {
      id: `maintenance_${Date.now()}`,
      title: 'Lembrete de Manutenção',
      body: `Está na hora de fazer a manutenção do seu ${equipmentType}. Agende já!`,
      type: 'maintenance',
      scheduledDate,
      data: {
        equipmentType,
        action: 'schedule_maintenance',
      },
    };

    await this.scheduleNotification(notification);
  }

  async schedulePromotionalNotification(
    title: string,
    message: string,
    daysFromNow: number = 7,
  ): Promise<void> {
    const scheduledDate = new Date();
    scheduledDate.setDate(scheduledDate.getDate() + daysFromNow);

    const notification: NotificationData = {
      id: `promo_${Date.now()}`,
      title,
      body: message,
      type: 'promotion',
      scheduledDate,
      data: {
        action: 'open_promotions',
      },
    };

    await this.scheduleNotification(notification);
  }

  private async scheduleDefaultNotifications(): Promise<void> {
    // Agendar notificações padrão
    await this.schedulePromotionalNotification(
      'Oferta Especial ROBFRIO!',
      'Desconto de 15% em manutenções preventivas. Válido até o final do mês!',
      3,
    );

    await this.scheduleMaintenanceReminder('Sistema de Refrigeração', 30);
    await this.scheduleMaintenanceReminder('Câmara Frigorífica', 60);
  }

  async getStoredNotifications(): Promise<NotificationData[]> {
    try {
      const stored = await AsyncStorage.getItem('notifications');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Erro ao obter notificações armazenadas:', error);
      return [];
    }
  }

  async clearAllNotifications(): Promise<void> {
    try {
      await AsyncStorage.removeItem('notifications');
      console.log('Todas as notificações foram removidas');
    } catch (error) {
      console.error('Erro ao limpar notificações:', error);
    }
  }

  async showLocalNotification(notification: NotificationData): Promise<void> {
    // Para demonstração, usar Alert
    Alert.alert(
      notification.title,
      notification.body,
      [
        {
          text: 'Fechar',
          style: 'cancel',
        },
        {
          text: 'Ver Mais',
          onPress: () => this.handleNotificationPress(notification),
        },
      ],
    );
  }

  private handleNotificationPress(notification: NotificationData): void {
    console.log('Notificação pressionada:', notification);
    // Implementar navegação baseada no tipo de notificação
    switch (notification.type) {
      case 'maintenance':
        // Navegar para tela de agendamento
        break;
      case 'promotion':
        // Navegar para tela de promoções
        break;
      case 'emergency':
        // Abrir contato de emergência
        break;
      default:
        break;
    }
  }

  // Método para testar notificações
  async testNotification(): Promise<void> {
    const testNotification: NotificationData = {
      id: `test_${Date.now()}`,
      title: 'Teste ROBFRIO',
      body: 'Esta é uma notificação de teste do aplicativo ROBFRIO!',
      type: 'reminder',
    };

    await this.showLocalNotification(testNotification);
  }
}

export default NotificationService;

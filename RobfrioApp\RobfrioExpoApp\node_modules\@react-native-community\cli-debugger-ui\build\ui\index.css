html,
body {
  font-family: Helvetica, Verdana, sans-serif;
  font-size: large;
  font-weight: 200;
  height: 100%;
  margin: 0;
  padding: 0;
}
.shortcut {
  border-radius: 4px;
  color: #eee;
  background-color: #333;
  font-family: 'Monaco', monospace;
  font-size: medium;
  letter-spacing: 3px;
  padding: 4px;
}
.content {
  padding: 10px;
}
.reload-btn {
  padding: 5px 10px;
}
body.dark {
  background-color: #242424;
  color: #afafaf;
}
.dark .shortcut {
  color: #c1c1c1;
}
.dark a {
  color: #3b99fc;
}
input[type='checkbox'] {
  vertical-align: middle;
}

.warning {
  box-sizing: border-box;
  max-width: 800px;
  padding: 16px;
  margin-bottom: 1em;
  border-left: 5px solid rgb(230, 167, 0);
  border-radius: 6px;
  background-color: rgb(255, 248, 230);
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
  color: rgb(77, 56, 0);
  font-size: 16px;
  font-weight: 500;
  line-height: 1.6;
}

.warning p {
  margin-top: 0;
}

.warning p:last-child {
  margin: 0;
}

.dark .warning {
  background: rgb(77, 56, 0);
  border-left-color: rgb(230, 167, 0);
  color: rgb(255, 248, 230);
}

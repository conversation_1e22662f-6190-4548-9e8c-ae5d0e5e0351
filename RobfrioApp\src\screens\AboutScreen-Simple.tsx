import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';

const AboutScreen: React.FC = () => {
  return (
    <ScrollView style={styles.container}>
      {/* Header Section */}
      <View style={styles.headerSection}>
        <Text style={styles.headerTitle}>Sobre a ROBFRIO</Text>
        <Text style={styles.headerSubtitle}>
          Tradição e inovação em refrigeração comercial
        </Text>
      </View>

      {/* Company Story */}
      <View style={styles.storySection}>
        <Text style={styles.sectionTitle}>Nossa História</Text>
        <Text style={styles.storyText}>
          Desde 2011, a ROBFRIO tem se destacado no mercado de refrigeração comercial, 
          oferecendo soluções personalizadas e de alta qualidade para empresas de todos os portes.
        </Text>
        <Text style={styles.storyText}>
          Nossa missão é proporcionar sistemas de refrigeração eficientes e duradouros, 
          garantindo a conservação perfeita dos seus produtos e a satisfação dos seus clientes.
        </Text>
      </View>

      {/* Values Section */}
      <View style={styles.valuesSection}>
        <Text style={styles.sectionTitle}>Nossos Valores</Text>
        
        <View style={styles.valueCard}>
          <View style={styles.valueIcon}>
            <Text style={styles.valueEmoji}>⭐</Text>
          </View>
          <View style={styles.valueContent}>
            <Text style={styles.valueTitle}>Qualidade</Text>
            <Text style={styles.valueDescription}>
              Equipamentos e serviços de primeira linha para garantir a máxima eficiência
            </Text>
          </View>
        </View>

        <View style={styles.valueCard}>
          <View style={[styles.valueIcon, {backgroundColor: '#f97316'}]}>
            <Text style={styles.valueEmoji}>🤝</Text>
          </View>
          <View style={styles.valueContent}>
            <Text style={styles.valueTitle}>Confiança</Text>
            <Text style={styles.valueDescription}>
              Relacionamento duradouro baseado na transparência e comprometimento
            </Text>
          </View>
        </View>

        <View style={[styles.valueCard, {marginBottom: 0}]}>
          <View style={[styles.valueIcon, {backgroundColor: '#10b981'}]}>
            <Text style={styles.valueEmoji}>🔧</Text>
          </View>
          <View style={styles.valueContent}>
            <Text style={styles.valueTitle}>Expertise</Text>
            <Text style={styles.valueDescription}>
              Equipe técnica especializada com anos de experiência no mercado
            </Text>
          </View>
        </View>
      </View>

      {/* Statistics */}
      <View style={styles.statsSection}>
        <Text style={styles.sectionTitle}>Números que Impressionam</Text>
        <View style={styles.statsGrid}>
          <View style={[styles.statCard, {backgroundColor: '#1e40af'}]}>
            <Text style={styles.statNumber}>13+</Text>
            <Text style={styles.statLabel}>Anos de Experiência</Text>
          </View>
          
          <View style={[styles.statCard, {backgroundColor: '#f97316'}]}>
            <Text style={styles.statNumber}>500+</Text>
            <Text style={styles.statLabel}>Projetos Realizados</Text>
          </View>
          
          <View style={[styles.statCard, {backgroundColor: '#10b981'}]}>
            <Text style={styles.statNumber}>100%</Text>
            <Text style={styles.statLabel}>Clientes Satisfeitos</Text>
          </View>
          
          <View style={[styles.statCard, {backgroundColor: '#8b5cf6'}]}>
            <Text style={styles.statNumber}>24h</Text>
            <Text style={styles.statLabel}>Suporte Emergencial</Text>
          </View>
        </View>
      </View>

      {/* Mission & Vision */}
      <View style={styles.missionSection}>
        <View style={styles.missionCard}>
          <Text style={styles.missionIcon}>🎯</Text>
          <Text style={styles.missionTitle}>Missão</Text>
          <Text style={styles.missionText}>
            Proporcionar soluções completas em refrigeração comercial, garantindo 
            a conservação perfeita dos produtos de nossos clientes com tecnologia 
            de ponta e atendimento personalizado.
          </Text>
        </View>

        <View style={styles.missionCard}>
          <Text style={styles.missionIcon}>👁️</Text>
          <Text style={styles.missionTitle}>Visão</Text>
          <Text style={styles.missionText}>
            Ser reconhecida como a empresa líder em refrigeração comercial, 
            inovando constantemente e expandindo nossa presença no mercado nacional.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  headerSection: {
    backgroundColor: '#1e40af',
    paddingHorizontal: 20,
    paddingVertical: 30,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#e2e8f0',
    textAlign: 'center',
  },
  storySection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 16,
    textAlign: 'center',
  },
  storyText: {
    fontSize: 16,
    color: '#64748b',
    lineHeight: 24,
    marginBottom: 16,
    textAlign: 'justify',
  },
  valuesSection: {
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  valueCard: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  valueIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#1e40af',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  valueEmoji: {
    fontSize: 24,
  },
  valueContent: {
    flex: 1,
  },
  valueTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 4,
  },
  valueDescription: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
  statsSection: {
    padding: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    width: '47%',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  statNumber: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#ffffff',
    textAlign: 'center',
    opacity: 0.9,
  },
  missionSection: {
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  missionCard: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  missionIcon: {
    fontSize: 32,
    marginBottom: 12,
  },
  missionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 12,
  },
  missionText: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default AboutScreen;

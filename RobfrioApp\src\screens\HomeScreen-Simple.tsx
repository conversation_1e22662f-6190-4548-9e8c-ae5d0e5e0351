import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';

const HomeScreen: React.FC = () => {
  const handleWhatsApp = () => {
    Alert.alert(
      '📱 WhatsApp',
      'Abrindo WhatsApp para contato...',
      [{text: 'OK', onPress: () => {
        const message = 'Olá! Gostaria de solicitar um orçamento para serviços de refrigeração.';
        const url = `whatsapp://send?phone=5511999999999&text=${encodeURIComponent(message)}`;
        Linking.openURL(url).catch(() => {
          Linking.openURL(`https://wa.me/5511999999999?text=${encodeURIComponent(message)}`);
        });
      }}]
    );
  };

  const handleCall = () => {
    Alert.alert(
      '📞 Telefone',
      'Ligando para (11) 99999-9999...',
      [{text: 'OK', onPress: () => Linking.openURL('tel:+5511999999999')}]
    );
  };

  return (
    <ScrollView style={styles.container}>
      {/* Hero Section */}
      <View style={styles.heroSection}>
        <View style={styles.heroContent}>
          <Text style={styles.heroTitle}>
            Soluções Completas em{'\n'}
            <Text style={styles.heroAccent}>Refrigeração Comercial</Text>
          </Text>
          <Text style={styles.heroDescription}>
            Há mais de 13 anos no mercado, a ROBFRIO oferece serviços especializados em 
            refrigeração comercial e montagem de câmaras frigoríficas.
          </Text>
          <View style={styles.heroButtons}>
            <TouchableOpacity style={styles.primaryButton} onPress={handleWhatsApp}>
              <Text style={styles.primaryButtonText}>📱 Solicitar Orçamento</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.secondaryButton} onPress={handleCall}>
              <Text style={styles.secondaryButtonText}>📞 Ligar Agora</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Stats Section */}
      <View style={styles.statsSection}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>13+</Text>
          <Text style={styles.statLabel}>Anos de Experiência</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>500+</Text>
          <Text style={styles.statLabel}>Projetos Realizados</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>100%</Text>
          <Text style={styles.statLabel}>Clientes Satisfeitos</Text>
        </View>
      </View>

      {/* Services Preview */}
      <View style={styles.servicesPreview}>
        <Text style={styles.sectionTitle}>Nossos Serviços</Text>
        <View style={styles.serviceCards}>
          <View style={styles.serviceCard}>
            <View style={styles.serviceIcon}>
              <Text style={styles.serviceEmoji}>❄️</Text>
            </View>
            <Text style={styles.serviceTitle}>Refrigeração Comercial</Text>
            <Text style={styles.serviceDescription}>
              Instalação, manutenção e modernização de sistemas completos
            </Text>
          </View>
          
          <View style={styles.serviceCard}>
            <View style={[styles.serviceIcon, {backgroundColor: '#f97316'}]}>
              <Text style={styles.serviceEmoji}>🏪</Text>
            </View>
            <Text style={styles.serviceTitle}>Câmaras Frigoríficas</Text>
            <Text style={styles.serviceDescription}>
              Projeto personalizado, montagem e sistemas de controle
            </Text>
          </View>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>Ações Rápidas</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={handleWhatsApp}>
            <Text style={styles.actionEmoji}>💬</Text>
            <Text style={styles.actionText}>WhatsApp</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleCall}>
            <Text style={styles.actionEmoji}>📞</Text>
            <Text style={styles.actionText}>Ligar</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton}>
            <Text style={styles.actionEmoji}>📧</Text>
            <Text style={styles.actionText}>E-mail</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton}>
            <Text style={styles.actionEmoji}>📍</Text>
            <Text style={styles.actionText}>Localização</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Company Info */}
      <View style={styles.companyInfo}>
        <Text style={styles.sectionTitle}>ROBFRIO</Text>
        <Text style={styles.companyDescription}>
          Especialista em refrigeração comercial e câmaras frigoríficas desde 2011. 
          Oferecemos soluções completas para seu negócio com qualidade e confiança.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  heroSection: {
    backgroundColor: '#1e40af',
    paddingHorizontal: 20,
    paddingVertical: 40,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  heroContent: {
    alignItems: 'center',
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 36,
  },
  heroAccent: {
    color: '#fbbf24',
  },
  heroDescription: {
    fontSize: 16,
    color: '#e2e8f0',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
    paddingHorizontal: 10,
  },
  heroButtons: {
    flexDirection: 'row',
    gap: 12,
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: '#f97316',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
  },
  secondaryButtonText: {
    color: '#1e40af',
    fontSize: 16,
    fontWeight: '600',
  },
  statsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 30,
    paddingHorizontal: 20,
    backgroundColor: '#f8fafc',
  },
  statCard: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    fontWeight: '500',
  },
  servicesPreview: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 20,
    textAlign: 'center',
  },
  serviceCards: {
    flexDirection: 'row',
    gap: 16,
  },
  serviceCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  serviceIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#1e40af',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  serviceEmoji: {
    fontSize: 30,
  },
  serviceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 8,
    textAlign: 'center',
  },
  serviceDescription: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 20,
  },
  quickActions: {
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    gap: 16,
  },
  actionButton: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    minWidth: 80,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  actionEmoji: {
    fontSize: 24,
    marginBottom: 8,
  },
  actionText: {
    fontSize: 12,
    color: '#64748b',
    fontWeight: '500',
  },
  companyInfo: {
    padding: 20,
    alignItems: 'center',
  },
  companyDescription: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default HomeScreen;

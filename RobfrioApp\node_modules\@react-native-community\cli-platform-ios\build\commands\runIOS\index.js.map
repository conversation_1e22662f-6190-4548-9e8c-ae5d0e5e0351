{"version": 3, "names": ["runIOS", "_", "ctx", "args", "link", "setPlatform", "reactNativeVersion", "setVersion", "project", "ios", "CLIError", "xcodeProject", "sourceDir", "process", "chdir", "binaryPath", "path", "isAbsolute", "join", "root", "fs", "existsSync", "configuration", "logger", "warn", "mode", "projectInfo", "getProjectInfo", "checkIfConfigurationExists", "inferredSchemeName", "basename", "name", "extname", "scheme", "interactive", "selection", "selectFromInteractiveMode", "modifiedArgs", "getConfigurationScheme", "info", "isWorkspace", "chalk", "bold", "availableDevices", "listIOSDevices", "listDevices", "device", "udid", "selected<PERSON><PERSON><PERSON>", "promptForDeviceSelection", "type", "runOnSimulator", "runOnDevice", "simulator", "bootedDevices", "filter", "isAvailable", "simulators", "getSimulators", "bootedSimulators", "Object", "keys", "devices", "map", "key", "reduce", "acc", "val", "concat", "state", "booted", "length", "runOnBootedDevicesSimulators", "error", "find", "d", "printFoundDevices", "physicalDevices", "matchingDevice", "selectedSimulator", "fallbackSimulators", "getDestinationSimulator", "activeDeveloperDir", "child_process", "execFileSync", "encoding", "trim", "bootSimulator", "buildOutput", "appPath", "buildProject", "getBuildPath", "spawnSync", "stdio", "bundleID", "result", "status", "success", "stderr", "toString", "isIOSDeployInstalled", "appProcess", "spawn", "detached", "unref", "iosDeployInstallArgs", "iosDeployOutput", "message", "simulatorFullName", "formattedDeviceName", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buildSettings", "settings", "JSON", "parse", "i", "wrapperExtension", "WRAPPER_EXTENSION", "targetBuildDir", "TARGET_BUILD_DIR", "executableFolderPath", "EXECUTABLE_FOLDER_PATH", "isCatalyst", "getPlatformName", "platformNameMatch", "exec", "deviceName", "firstIOSDevice", "undefined", "deviceByName", "String", "version", "description", "func", "examples", "desc", "cmd", "options", "iosBuildOptions"], "sources": ["../../../src/commands/runIOS/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport child_process from 'child_process';\nimport path from 'path';\nimport fs from 'fs';\nimport chalk from 'chalk';\nimport {Config, IOSProjectInfo} from '@react-native-community/cli-types';\nimport {getDestinationSimulator} from '../../tools/getDestinationSimulator';\nimport {logger, CLIError, link} from '@react-native-community/cli-tools';\nimport {BuildFlags, buildProject} from '../buildIOS/buildProject';\nimport {iosBuildOptions} from '../buildIOS';\nimport {Device} from '../../types';\nimport listIOSDevices from '../../tools/listIOSDevices';\nimport {checkIfConfigurationExists} from '../../tools/checkIfConfigurationExists';\nimport {getProjectInfo} from '../../tools/getProjectInfo';\nimport {getConfigurationScheme} from '../../tools/getConfigurationScheme';\nimport {selectFromInteractiveMode} from '../../tools/selectFromInteractiveMode';\nimport {promptForDeviceSelection} from '../../tools/prompts';\nimport getSimulators from '../../tools/getSimulators';\n\nexport interface FlagsT extends BuildFlags {\n  simulator?: string;\n  configuration: string;\n  scheme?: string;\n  projectPath: string;\n  device?: string | true;\n  udid?: string;\n  binaryPath?: string;\n  listDevices?: boolean;\n}\n\nasync function runIOS(_: Array<string>, ctx: Config, args: FlagsT) {\n  link.setPlatform('ios');\n\n  if (ctx.reactNativeVersion !== 'unknown') {\n    link.setVersion(ctx.reactNativeVersion);\n  }\n\n  if (!ctx.project.ios) {\n    throw new CLIError(\n      'iOS project folder not found. Are you sure this is a React Native project?',\n    );\n  }\n\n  const {xcodeProject, sourceDir} = ctx.project.ios;\n\n  if (!xcodeProject) {\n    throw new CLIError(\n      `Could not find Xcode project files in \"${sourceDir}\" folder`,\n    );\n  }\n\n  process.chdir(sourceDir);\n\n  if (args.binaryPath) {\n    args.binaryPath = path.isAbsolute(args.binaryPath)\n      ? args.binaryPath\n      : path.join(ctx.root, args.binaryPath);\n\n    if (!fs.existsSync(args.binaryPath)) {\n      throw new CLIError(\n        'binary-path was specified, but the file was not found.',\n      );\n    }\n  }\n\n  if (args.configuration) {\n    logger.warn('--configuration has been deprecated. Use --mode instead.');\n    logger.warn(\n      'Parameters were automatically reassigned to --mode on this run.',\n    );\n    args.mode = args.configuration;\n  }\n\n  const projectInfo = getProjectInfo();\n\n  if (args.mode) {\n    checkIfConfigurationExists(projectInfo, args.mode);\n  }\n\n  const inferredSchemeName = path.basename(\n    xcodeProject.name,\n    path.extname(xcodeProject.name),\n  );\n\n  let scheme = args.scheme || inferredSchemeName;\n  let mode = args.mode;\n\n  if (args.interactive) {\n    const selection = await selectFromInteractiveMode({scheme, mode});\n\n    if (selection.scheme) {\n      scheme = selection.scheme;\n    }\n\n    if (selection.mode) {\n      mode = selection.mode;\n    }\n  }\n\n  const modifiedArgs = {...args, scheme, mode};\n\n  modifiedArgs.mode = getConfigurationScheme(\n    {scheme: modifiedArgs.scheme, mode: modifiedArgs.mode},\n    sourceDir,\n  );\n\n  logger.info(\n    `Found Xcode ${\n      xcodeProject.isWorkspace ? 'workspace' : 'project'\n    } \"${chalk.bold(xcodeProject.name)}\"`,\n  );\n\n  const availableDevices = await listIOSDevices();\n  if (modifiedArgs.listDevices || modifiedArgs.interactive) {\n    if (modifiedArgs.device || modifiedArgs.udid) {\n      logger.warn(\n        `Both ${\n          modifiedArgs.device ? 'device' : 'udid'\n        } and \"list-devices\" parameters were passed to \"run\" command. We will list available devices and let you choose from one.`,\n      );\n    }\n    const selectedDevice = await promptForDeviceSelection(availableDevices);\n    if (!selectedDevice) {\n      throw new CLIError(\n        `Failed to select device, please try to run app without ${\n          args.listDevices ? 'list-devices' : 'interactive'\n        } command.`,\n      );\n    }\n    if (selectedDevice.type === 'simulator') {\n      return runOnSimulator(xcodeProject, scheme, modifiedArgs, selectedDevice);\n    } else {\n      return runOnDevice(selectedDevice, scheme, xcodeProject, modifiedArgs);\n    }\n  }\n\n  if (!modifiedArgs.device && !modifiedArgs.udid && !modifiedArgs.simulator) {\n    const bootedDevices = availableDevices.filter(\n      ({type, isAvailable}) => type === 'device' && isAvailable,\n    );\n\n    const simulators = getSimulators();\n    const bootedSimulators = Object.keys(simulators.devices)\n      .map((key) => simulators.devices[key])\n      .reduce((acc, val) => acc.concat(val), [])\n      .filter(({state}) => state === 'Booted');\n\n    const booted = [...bootedDevices, ...bootedSimulators];\n    if (booted.length === 0) {\n      logger.info(\n        'No booted devices or simulators found. Launching first available simulator...',\n      );\n      return runOnSimulator(xcodeProject, scheme, modifiedArgs);\n    }\n\n    logger.info(`Found booted ${booted.map(({name}) => name).join(', ')}`);\n\n    return runOnBootedDevicesSimulators(\n      scheme,\n      xcodeProject,\n      modifiedArgs,\n      bootedDevices,\n      bootedSimulators,\n    );\n  }\n\n  if (modifiedArgs.device && modifiedArgs.udid) {\n    return logger.error(\n      'The `device` and `udid` options are mutually exclusive.',\n    );\n  }\n\n  if (modifiedArgs.udid) {\n    const device = availableDevices.find((d) => d.udid === modifiedArgs.udid);\n    if (!device) {\n      return logger.error(\n        `Could not find a device with udid: \"${chalk.bold(\n          modifiedArgs.udid,\n        )}\". ${printFoundDevices(availableDevices)}`,\n      );\n    }\n    if (device.type === 'simulator') {\n      return runOnSimulator(xcodeProject, scheme, modifiedArgs);\n    } else {\n      return runOnDevice(device, scheme, xcodeProject, modifiedArgs);\n    }\n  } else if (modifiedArgs.device) {\n    const physicalDevices = availableDevices.filter(\n      ({type}) => type !== 'simulator',\n    );\n    const device = matchingDevice(physicalDevices, modifiedArgs.device);\n    if (device) {\n      return runOnDevice(device, scheme, xcodeProject, modifiedArgs);\n    }\n  } else {\n    runOnSimulator(xcodeProject, scheme, modifiedArgs);\n  }\n}\n\nasync function runOnBootedDevicesSimulators(\n  scheme: string,\n  xcodeProject: IOSProjectInfo,\n  args: FlagsT,\n  devices: Device[],\n  simulators: Device[],\n) {\n  for (const device of devices) {\n    await runOnDevice(device, scheme, xcodeProject, args);\n  }\n\n  for (const simulator of simulators) {\n    await runOnSimulator(xcodeProject, scheme, args, simulator);\n  }\n}\n\nasync function runOnSimulator(\n  xcodeProject: IOSProjectInfo,\n  scheme: string,\n  args: FlagsT,\n  simulator?: Device,\n) {\n  // let selectedSimulator;\n  /**\n   * If provided simulator does not exist, try simulators in following order\n   * - iPhone 14\n   * - iPhone 13\n   * - iPhone 12\n   * - iPhone 11\n   */\n\n  let selectedSimulator;\n  if (simulator) {\n    selectedSimulator = simulator;\n  } else {\n    const fallbackSimulators = [\n      'iPhone 14',\n      'iPhone 13',\n      'iPhone 12',\n      'iPhone 11',\n    ];\n    selectedSimulator = getDestinationSimulator(args, fallbackSimulators);\n  }\n\n  if (!selectedSimulator) {\n    throw new CLIError(\n      `No simulator available with ${\n        args.simulator ? `name \"${args.simulator}\"` : `udid \"${args.udid}\"`\n      }`,\n    );\n  }\n\n  /**\n   * Booting simulator through `xcrun simctl boot` will boot it in the `headless` mode\n   * (running in the background).\n   *\n   * In order for user to see the app and the simulator itself, we have to make sure\n   * that the Simulator.app is running.\n   *\n   * We also pass it `-CurrentDeviceUDID` so that when we launch it for the first time,\n   * it will not boot the \"default\" device, but the one we set. If the app is already running,\n   * this flag has no effect.\n   */\n  const activeDeveloperDir = child_process\n    .execFileSync('xcode-select', ['-p'], {encoding: 'utf8'})\n    .trim();\n\n  child_process.execFileSync('open', [\n    `${activeDeveloperDir}/Applications/Simulator.app`,\n    '--args',\n    '-CurrentDeviceUDID',\n    selectedSimulator.udid,\n  ]);\n\n  if (!selectedSimulator.booted) {\n    bootSimulator(selectedSimulator);\n  }\n\n  let buildOutput, appPath;\n  if (!args.binaryPath) {\n    buildOutput = await buildProject(\n      xcodeProject,\n      selectedSimulator.udid,\n      scheme,\n      args,\n    );\n\n    appPath = getBuildPath(\n      xcodeProject,\n      args.mode || args.configuration,\n      buildOutput,\n      scheme,\n    );\n  } else {\n    appPath = args.binaryPath;\n  }\n\n  logger.info(\n    `Installing \"${chalk.bold(appPath)} on ${selectedSimulator.name}\"`,\n  );\n\n  child_process.spawnSync(\n    'xcrun',\n    ['simctl', 'install', selectedSimulator.udid, appPath],\n    {stdio: 'inherit'},\n  );\n\n  const bundleID = child_process\n    .execFileSync(\n      '/usr/libexec/PlistBuddy',\n      ['-c', 'Print:CFBundleIdentifier', path.join(appPath, 'Info.plist')],\n      {encoding: 'utf8'},\n    )\n    .trim();\n\n  logger.info(`Launching \"${chalk.bold(bundleID)}\"`);\n\n  const result = child_process.spawnSync('xcrun', [\n    'simctl',\n    'launch',\n    selectedSimulator.udid,\n    bundleID,\n  ]);\n\n  if (result.status === 0) {\n    logger.success('Successfully launched the app on the simulator');\n  } else {\n    logger.error(\n      'Failed to launch the app on simulator',\n      result.stderr.toString(),\n    );\n  }\n}\n\nasync function runOnDevice(\n  selectedDevice: Device,\n  scheme: string,\n  xcodeProject: IOSProjectInfo,\n  args: FlagsT,\n) {\n  if (args.binaryPath && selectedDevice.type === 'catalyst') {\n    throw new CLIError(\n      'binary-path was specified for catalyst device, which is not supported.',\n    );\n  }\n\n  const isIOSDeployInstalled = child_process.spawnSync(\n    'ios-deploy',\n    ['--version'],\n    {encoding: 'utf8'},\n  );\n\n  if (isIOSDeployInstalled.error) {\n    throw new CLIError(\n      `Failed to install the app on the device because we couldn't execute the \"ios-deploy\" command. Please install it by running \"${chalk.bold(\n        'brew install ios-deploy',\n      )}\" and try again.`,\n    );\n  }\n\n  if (selectedDevice.type === 'catalyst') {\n    const buildOutput = await buildProject(\n      xcodeProject,\n      selectedDevice.udid,\n      scheme,\n      args,\n    );\n\n    const appPath = getBuildPath(\n      xcodeProject,\n      args.mode || args.configuration,\n      buildOutput,\n      scheme,\n      true,\n    );\n    const appProcess = child_process.spawn(`${appPath}/${scheme}`, [], {\n      detached: true,\n      stdio: 'ignore',\n    });\n    appProcess.unref();\n  } else {\n    let buildOutput, appPath;\n    if (!args.binaryPath) {\n      buildOutput = await buildProject(\n        xcodeProject,\n        selectedDevice.udid,\n        scheme,\n        args,\n      );\n\n      appPath = getBuildPath(\n        xcodeProject,\n        args.mode || args.configuration,\n        buildOutput,\n        scheme,\n      );\n    } else {\n      appPath = args.binaryPath;\n    }\n\n    const iosDeployInstallArgs = [\n      '--bundle',\n      appPath,\n      '--id',\n      selectedDevice.udid,\n      '--justlaunch',\n    ];\n\n    logger.info(`Installing and launching your app on ${selectedDevice.name}`);\n\n    const iosDeployOutput = child_process.spawnSync(\n      'ios-deploy',\n      iosDeployInstallArgs,\n      {encoding: 'utf8'},\n    );\n\n    if (iosDeployOutput.error) {\n      throw new CLIError(\n        `Failed to install the app on the device. We've encountered an error in \"ios-deploy\" command: ${iosDeployOutput.error.message}`,\n      );\n    }\n  }\n\n  return logger.success('Installed the app on the device.');\n}\n\nfunction bootSimulator(selectedSimulator: Device) {\n  const simulatorFullName = formattedDeviceName(selectedSimulator);\n  logger.info(`Launching ${simulatorFullName}`);\n\n  child_process.spawnSync('xcrun', ['simctl', 'boot', selectedSimulator.udid]);\n}\n\nfunction getTargetPaths(buildSettings: string) {\n  const settings = JSON.parse(buildSettings);\n\n  // Find app in all building settings - look for WRAPPER_EXTENSION: 'app',\n  for (const i in settings) {\n    const wrapperExtension = settings[i].buildSettings.WRAPPER_EXTENSION;\n\n    if (wrapperExtension === 'app') {\n      return {\n        targetBuildDir: settings[i].buildSettings.TARGET_BUILD_DIR,\n        executableFolderPath: settings[i].buildSettings.EXECUTABLE_FOLDER_PATH,\n      };\n    }\n  }\n\n  return {};\n}\n\nfunction getBuildPath(\n  xcodeProject: IOSProjectInfo,\n  mode: BuildFlags['mode'],\n  buildOutput: string,\n  scheme: string,\n  isCatalyst: boolean = false,\n) {\n  const buildSettings = child_process.execFileSync(\n    'xcodebuild',\n    [\n      xcodeProject.isWorkspace ? '-workspace' : '-project',\n      xcodeProject.name,\n      '-scheme',\n      scheme,\n      '-sdk',\n      getPlatformName(buildOutput),\n      '-configuration',\n      mode,\n      '-showBuildSettings',\n      '-json',\n    ],\n    {encoding: 'utf8'},\n  );\n  const {targetBuildDir, executableFolderPath} = getTargetPaths(buildSettings);\n\n  if (!targetBuildDir) {\n    throw new CLIError('Failed to get the target build directory.');\n  }\n\n  if (!executableFolderPath) {\n    throw new CLIError('Failed to get the app name.');\n  }\n\n  return `${targetBuildDir}${\n    isCatalyst ? '-maccatalyst' : ''\n  }/${executableFolderPath}`;\n}\n\nfunction getPlatformName(buildOutput: string) {\n  // Xcode can sometimes escape `=` with a backslash or put the value in quotes\n  const platformNameMatch = /export PLATFORM_NAME\\\\?=\"?(\\w+)\"?$/m.exec(\n    buildOutput,\n  );\n  if (!platformNameMatch) {\n    throw new CLIError(\n      'Couldn\\'t find \"PLATFORM_NAME\" variable in xcodebuild output. Please report this issue and run your project with Xcode instead.',\n    );\n  }\n  return platformNameMatch[1];\n}\n\nfunction matchingDevice(\n  devices: Array<Device>,\n  deviceName: string | true | undefined,\n) {\n  if (deviceName === true) {\n    const firstIOSDevice = devices.find((d) => d.type === 'device')!;\n    if (firstIOSDevice) {\n      logger.info(\n        `Using first available device named \"${chalk.bold(\n          firstIOSDevice.name,\n        )}\" due to lack of name supplied.`,\n      );\n      return firstIOSDevice;\n    } else {\n      logger.error('No iOS devices connected.');\n      return undefined;\n    }\n  }\n  const deviceByName = devices.find(\n    (device) =>\n      device.name === deviceName || formattedDeviceName(device) === deviceName,\n  );\n  if (!deviceByName) {\n    logger.error(\n      `Could not find a device named: \"${chalk.bold(\n        String(deviceName),\n      )}\". ${printFoundDevices(devices)}`,\n    );\n  }\n  return deviceByName;\n}\n\nfunction formattedDeviceName(simulator: Device) {\n  return simulator.version\n    ? `${simulator.name} (${simulator.version})`\n    : simulator.name;\n}\n\nfunction printFoundDevices(devices: Array<Device>) {\n  return [\n    'Available devices:',\n    ...devices.map((device) => `  - ${device.name} (${device.udid})`),\n  ].join('\\n');\n}\n\nexport default {\n  name: 'run-ios',\n  description: 'builds your app and starts it on iOS simulator',\n  func: runIOS,\n  examples: [\n    {\n      desc: 'Run on a different simulator, e.g. iPhone SE (2nd generation)',\n      cmd: 'react-native run-ios --simulator \"iPhone SE (2nd generation)\"',\n    },\n    {\n      desc: \"Run on a connected device, e.g. Max's iPhone\",\n      cmd: 'react-native run-ios --device \"Max\\'s iPhone\"',\n    },\n    {\n      desc: 'Run on the AppleTV simulator',\n      cmd:\n        'react-native run-ios --simulator \"Apple TV\"  --scheme \"helloworld-tvOS\"',\n    },\n  ],\n  options: [\n    ...iosBuildOptions,\n    {\n      name: '--no-packager',\n      description: 'Do not launch packager while building',\n    },\n    {\n      name: '--binary-path <string>',\n      description:\n        'Path relative to project root where pre-built .app binary lives.',\n    },\n    {\n      name: '--list-devices',\n      description:\n        'List all available iOS devices and simulators and let you choose one to run the app. ',\n    },\n    {\n      name: '--interactive',\n      description:\n        'Explicitly select which scheme and configuration to use before running a build and select device to run the application.',\n    },\n  ],\n};\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAsD;AAxBtD;AACA;AACA;AACA;AACA;AACA;AACA;;AA+BA,eAAeA,MAAM,CAACC,CAAgB,EAAEC,GAAW,EAAEC,IAAY,EAAE;EACjEC,gBAAI,CAACC,WAAW,CAAC,KAAK,CAAC;EAEvB,IAAIH,GAAG,CAACI,kBAAkB,KAAK,SAAS,EAAE;IACxCF,gBAAI,CAACG,UAAU,CAACL,GAAG,CAACI,kBAAkB,CAAC;EACzC;EAEA,IAAI,CAACJ,GAAG,CAACM,OAAO,CAACC,GAAG,EAAE;IACpB,MAAM,KAAIC,oBAAQ,EAChB,4EAA4E,CAC7E;EACH;EAEA,MAAM;IAACC,YAAY;IAAEC;EAAS,CAAC,GAAGV,GAAG,CAACM,OAAO,CAACC,GAAG;EAEjD,IAAI,CAACE,YAAY,EAAE;IACjB,MAAM,KAAID,oBAAQ,EACf,0CAAyCE,SAAU,UAAS,CAC9D;EACH;EAEAC,OAAO,CAACC,KAAK,CAACF,SAAS,CAAC;EAExB,IAAIT,IAAI,CAACY,UAAU,EAAE;IACnBZ,IAAI,CAACY,UAAU,GAAGC,eAAI,CAACC,UAAU,CAACd,IAAI,CAACY,UAAU,CAAC,GAC9CZ,IAAI,CAACY,UAAU,GACfC,eAAI,CAACE,IAAI,CAAChB,GAAG,CAACiB,IAAI,EAAEhB,IAAI,CAACY,UAAU,CAAC;IAExC,IAAI,CAACK,aAAE,CAACC,UAAU,CAAClB,IAAI,CAACY,UAAU,CAAC,EAAE;MACnC,MAAM,KAAIL,oBAAQ,EAChB,wDAAwD,CACzD;IACH;EACF;EAEA,IAAIP,IAAI,CAACmB,aAAa,EAAE;IACtBC,kBAAM,CAACC,IAAI,CAAC,0DAA0D,CAAC;IACvED,kBAAM,CAACC,IAAI,CACT,iEAAiE,CAClE;IACDrB,IAAI,CAACsB,IAAI,GAAGtB,IAAI,CAACmB,aAAa;EAChC;EAEA,MAAMI,WAAW,GAAG,IAAAC,8BAAc,GAAE;EAEpC,IAAIxB,IAAI,CAACsB,IAAI,EAAE;IACb,IAAAG,sDAA0B,EAACF,WAAW,EAAEvB,IAAI,CAACsB,IAAI,CAAC;EACpD;EAEA,MAAMI,kBAAkB,GAAGb,eAAI,CAACc,QAAQ,CACtCnB,YAAY,CAACoB,IAAI,EACjBf,eAAI,CAACgB,OAAO,CAACrB,YAAY,CAACoB,IAAI,CAAC,CAChC;EAED,IAAIE,MAAM,GAAG9B,IAAI,CAAC8B,MAAM,IAAIJ,kBAAkB;EAC9C,IAAIJ,IAAI,GAAGtB,IAAI,CAACsB,IAAI;EAEpB,IAAItB,IAAI,CAAC+B,WAAW,EAAE;IACpB,MAAMC,SAAS,GAAG,MAAM,IAAAC,oDAAyB,EAAC;MAACH,MAAM;MAAER;IAAI,CAAC,CAAC;IAEjE,IAAIU,SAAS,CAACF,MAAM,EAAE;MACpBA,MAAM,GAAGE,SAAS,CAACF,MAAM;IAC3B;IAEA,IAAIE,SAAS,CAACV,IAAI,EAAE;MAClBA,IAAI,GAAGU,SAAS,CAACV,IAAI;IACvB;EACF;EAEA,MAAMY,YAAY,GAAG;IAAC,GAAGlC,IAAI;IAAE8B,MAAM;IAAER;EAAI,CAAC;EAE5CY,YAAY,CAACZ,IAAI,GAAG,IAAAa,8CAAsB,EACxC;IAACL,MAAM,EAAEI,YAAY,CAACJ,MAAM;IAAER,IAAI,EAAEY,YAAY,CAACZ;EAAI,CAAC,EACtDb,SAAS,CACV;EAEDW,kBAAM,CAACgB,IAAI,CACR,eACC5B,YAAY,CAAC6B,WAAW,GAAG,WAAW,GAAG,SAC1C,KAAIC,gBAAK,CAACC,IAAI,CAAC/B,YAAY,CAACoB,IAAI,CAAE,GAAE,CACtC;EAED,MAAMY,gBAAgB,GAAG,MAAM,IAAAC,uBAAc,GAAE;EAC/C,IAAIP,YAAY,CAACQ,WAAW,IAAIR,YAAY,CAACH,WAAW,EAAE;IACxD,IAAIG,YAAY,CAACS,MAAM,IAAIT,YAAY,CAACU,IAAI,EAAE;MAC5CxB,kBAAM,CAACC,IAAI,CACR,QACCa,YAAY,CAACS,MAAM,GAAG,QAAQ,GAAG,MAClC,0HAAyH,CAC3H;IACH;IACA,MAAME,cAAc,GAAG,MAAM,IAAAC,iCAAwB,EAACN,gBAAgB,CAAC;IACvE,IAAI,CAACK,cAAc,EAAE;MACnB,MAAM,KAAItC,oBAAQ,EACf,0DACCP,IAAI,CAAC0C,WAAW,GAAG,cAAc,GAAG,aACrC,WAAU,CACZ;IACH;IACA,IAAIG,cAAc,CAACE,IAAI,KAAK,WAAW,EAAE;MACvC,OAAOC,cAAc,CAACxC,YAAY,EAAEsB,MAAM,EAAEI,YAAY,EAAEW,cAAc,CAAC;IAC3E,CAAC,MAAM;MACL,OAAOI,WAAW,CAACJ,cAAc,EAAEf,MAAM,EAAEtB,YAAY,EAAE0B,YAAY,CAAC;IACxE;EACF;EAEA,IAAI,CAACA,YAAY,CAACS,MAAM,IAAI,CAACT,YAAY,CAACU,IAAI,IAAI,CAACV,YAAY,CAACgB,SAAS,EAAE;IACzE,MAAMC,aAAa,GAAGX,gBAAgB,CAACY,MAAM,CAC3C,CAAC;MAACL,IAAI;MAAEM;IAAW,CAAC,KAAKN,IAAI,KAAK,QAAQ,IAAIM,WAAW,CAC1D;IAED,MAAMC,UAAU,GAAG,IAAAC,sBAAa,GAAE;IAClC,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,IAAI,CAACJ,UAAU,CAACK,OAAO,CAAC,CACrDC,GAAG,CAAEC,GAAG,IAAKP,UAAU,CAACK,OAAO,CAACE,GAAG,CAAC,CAAC,CACrCC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,CAACE,MAAM,CAACD,GAAG,CAAC,EAAE,EAAE,CAAC,CACzCZ,MAAM,CAAC,CAAC;MAACc;IAAK,CAAC,KAAKA,KAAK,KAAK,QAAQ,CAAC;IAE1C,MAAMC,MAAM,GAAG,CAAC,GAAGhB,aAAa,EAAE,GAAGK,gBAAgB,CAAC;IACtD,IAAIW,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MACvBhD,kBAAM,CAACgB,IAAI,CACT,+EAA+E,CAChF;MACD,OAAOY,cAAc,CAACxC,YAAY,EAAEsB,MAAM,EAAEI,YAAY,CAAC;IAC3D;IAEAd,kBAAM,CAACgB,IAAI,CAAE,gBAAe+B,MAAM,CAACP,GAAG,CAAC,CAAC;MAAChC;IAAI,CAAC,KAAKA,IAAI,CAAC,CAACb,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;IAEtE,OAAOsD,4BAA4B,CACjCvC,MAAM,EACNtB,YAAY,EACZ0B,YAAY,EACZiB,aAAa,EACbK,gBAAgB,CACjB;EACH;EAEA,IAAItB,YAAY,CAACS,MAAM,IAAIT,YAAY,CAACU,IAAI,EAAE;IAC5C,OAAOxB,kBAAM,CAACkD,KAAK,CACjB,yDAAyD,CAC1D;EACH;EAEA,IAAIpC,YAAY,CAACU,IAAI,EAAE;IACrB,MAAMD,MAAM,GAAGH,gBAAgB,CAAC+B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC5B,IAAI,KAAKV,YAAY,CAACU,IAAI,CAAC;IACzE,IAAI,CAACD,MAAM,EAAE;MACX,OAAOvB,kBAAM,CAACkD,KAAK,CAChB,uCAAsChC,gBAAK,CAACC,IAAI,CAC/CL,YAAY,CAACU,IAAI,CACjB,MAAK6B,iBAAiB,CAACjC,gBAAgB,CAAE,EAAC,CAC7C;IACH;IACA,IAAIG,MAAM,CAACI,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAOC,cAAc,CAACxC,YAAY,EAAEsB,MAAM,EAAEI,YAAY,CAAC;IAC3D,CAAC,MAAM;MACL,OAAOe,WAAW,CAACN,MAAM,EAAEb,MAAM,EAAEtB,YAAY,EAAE0B,YAAY,CAAC;IAChE;EACF,CAAC,MAAM,IAAIA,YAAY,CAACS,MAAM,EAAE;IAC9B,MAAM+B,eAAe,GAAGlC,gBAAgB,CAACY,MAAM,CAC7C,CAAC;MAACL;IAAI,CAAC,KAAKA,IAAI,KAAK,WAAW,CACjC;IACD,MAAMJ,MAAM,GAAGgC,cAAc,CAACD,eAAe,EAAExC,YAAY,CAACS,MAAM,CAAC;IACnE,IAAIA,MAAM,EAAE;MACV,OAAOM,WAAW,CAACN,MAAM,EAAEb,MAAM,EAAEtB,YAAY,EAAE0B,YAAY,CAAC;IAChE;EACF,CAAC,MAAM;IACLc,cAAc,CAACxC,YAAY,EAAEsB,MAAM,EAAEI,YAAY,CAAC;EACpD;AACF;AAEA,eAAemC,4BAA4B,CACzCvC,MAAc,EACdtB,YAA4B,EAC5BR,IAAY,EACZ2D,OAAiB,EACjBL,UAAoB,EACpB;EACA,KAAK,MAAMX,MAAM,IAAIgB,OAAO,EAAE;IAC5B,MAAMV,WAAW,CAACN,MAAM,EAAEb,MAAM,EAAEtB,YAAY,EAAER,IAAI,CAAC;EACvD;EAEA,KAAK,MAAMkD,SAAS,IAAII,UAAU,EAAE;IAClC,MAAMN,cAAc,CAACxC,YAAY,EAAEsB,MAAM,EAAE9B,IAAI,EAAEkD,SAAS,CAAC;EAC7D;AACF;AAEA,eAAeF,cAAc,CAC3BxC,YAA4B,EAC5BsB,MAAc,EACd9B,IAAY,EACZkD,SAAkB,EAClB;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;;EAEE,IAAI0B,iBAAiB;EACrB,IAAI1B,SAAS,EAAE;IACb0B,iBAAiB,GAAG1B,SAAS;EAC/B,CAAC,MAAM;IACL,MAAM2B,kBAAkB,GAAG,CACzB,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,CACZ;IACDD,iBAAiB,GAAG,IAAAE,gDAAuB,EAAC9E,IAAI,EAAE6E,kBAAkB,CAAC;EACvE;EAEA,IAAI,CAACD,iBAAiB,EAAE;IACtB,MAAM,KAAIrE,oBAAQ,EACf,+BACCP,IAAI,CAACkD,SAAS,GAAI,SAAQlD,IAAI,CAACkD,SAAU,GAAE,GAAI,SAAQlD,IAAI,CAAC4C,IAAK,GAClE,EAAC,CACH;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMmC,kBAAkB,GAAGC,wBAAa,CACrCC,YAAY,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,EAAE;IAACC,QAAQ,EAAE;EAAM,CAAC,CAAC,CACxDC,IAAI,EAAE;EAETH,wBAAa,CAACC,YAAY,CAAC,MAAM,EAAE,CAChC,GAAEF,kBAAmB,6BAA4B,EAClD,QAAQ,EACR,oBAAoB,EACpBH,iBAAiB,CAAChC,IAAI,CACvB,CAAC;EAEF,IAAI,CAACgC,iBAAiB,CAACT,MAAM,EAAE;IAC7BiB,aAAa,CAACR,iBAAiB,CAAC;EAClC;EAEA,IAAIS,WAAW,EAAEC,OAAO;EACxB,IAAI,CAACtF,IAAI,CAACY,UAAU,EAAE;IACpByE,WAAW,GAAG,MAAM,IAAAE,0BAAY,EAC9B/E,YAAY,EACZoE,iBAAiB,CAAChC,IAAI,EACtBd,MAAM,EACN9B,IAAI,CACL;IAEDsF,OAAO,GAAGE,YAAY,CACpBhF,YAAY,EACZR,IAAI,CAACsB,IAAI,IAAItB,IAAI,CAACmB,aAAa,EAC/BkE,WAAW,EACXvD,MAAM,CACP;EACH,CAAC,MAAM;IACLwD,OAAO,GAAGtF,IAAI,CAACY,UAAU;EAC3B;EAEAQ,kBAAM,CAACgB,IAAI,CACR,eAAcE,gBAAK,CAACC,IAAI,CAAC+C,OAAO,CAAE,OAAMV,iBAAiB,CAAChD,IAAK,GAAE,CACnE;EAEDoD,wBAAa,CAACS,SAAS,CACrB,OAAO,EACP,CAAC,QAAQ,EAAE,SAAS,EAAEb,iBAAiB,CAAChC,IAAI,EAAE0C,OAAO,CAAC,EACtD;IAACI,KAAK,EAAE;EAAS,CAAC,CACnB;EAED,MAAMC,QAAQ,GAAGX,wBAAa,CAC3BC,YAAY,CACX,yBAAyB,EACzB,CAAC,IAAI,EAAE,0BAA0B,EAAEpE,eAAI,CAACE,IAAI,CAACuE,OAAO,EAAE,YAAY,CAAC,CAAC,EACpE;IAACJ,QAAQ,EAAE;EAAM,CAAC,CACnB,CACAC,IAAI,EAAE;EAET/D,kBAAM,CAACgB,IAAI,CAAE,cAAaE,gBAAK,CAACC,IAAI,CAACoD,QAAQ,CAAE,GAAE,CAAC;EAElD,MAAMC,MAAM,GAAGZ,wBAAa,CAACS,SAAS,CAAC,OAAO,EAAE,CAC9C,QAAQ,EACR,QAAQ,EACRb,iBAAiB,CAAChC,IAAI,EACtB+C,QAAQ,CACT,CAAC;EAEF,IAAIC,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;IACvBzE,kBAAM,CAAC0E,OAAO,CAAC,gDAAgD,CAAC;EAClE,CAAC,MAAM;IACL1E,kBAAM,CAACkD,KAAK,CACV,uCAAuC,EACvCsB,MAAM,CAACG,MAAM,CAACC,QAAQ,EAAE,CACzB;EACH;AACF;AAEA,eAAe/C,WAAW,CACxBJ,cAAsB,EACtBf,MAAc,EACdtB,YAA4B,EAC5BR,IAAY,EACZ;EACA,IAAIA,IAAI,CAACY,UAAU,IAAIiC,cAAc,CAACE,IAAI,KAAK,UAAU,EAAE;IACzD,MAAM,KAAIxC,oBAAQ,EAChB,wEAAwE,CACzE;EACH;EAEA,MAAM0F,oBAAoB,GAAGjB,wBAAa,CAACS,SAAS,CAClD,YAAY,EACZ,CAAC,WAAW,CAAC,EACb;IAACP,QAAQ,EAAE;EAAM,CAAC,CACnB;EAED,IAAIe,oBAAoB,CAAC3B,KAAK,EAAE;IAC9B,MAAM,KAAI/D,oBAAQ,EACf,+HAA8H+B,gBAAK,CAACC,IAAI,CACvI,yBAAyB,CACzB,kBAAiB,CACpB;EACH;EAEA,IAAIM,cAAc,CAACE,IAAI,KAAK,UAAU,EAAE;IACtC,MAAMsC,WAAW,GAAG,MAAM,IAAAE,0BAAY,EACpC/E,YAAY,EACZqC,cAAc,CAACD,IAAI,EACnBd,MAAM,EACN9B,IAAI,CACL;IAED,MAAMsF,OAAO,GAAGE,YAAY,CAC1BhF,YAAY,EACZR,IAAI,CAACsB,IAAI,IAAItB,IAAI,CAACmB,aAAa,EAC/BkE,WAAW,EACXvD,MAAM,EACN,IAAI,CACL;IACD,MAAMoE,UAAU,GAAGlB,wBAAa,CAACmB,KAAK,CAAE,GAAEb,OAAQ,IAAGxD,MAAO,EAAC,EAAE,EAAE,EAAE;MACjEsE,QAAQ,EAAE,IAAI;MACdV,KAAK,EAAE;IACT,CAAC,CAAC;IACFQ,UAAU,CAACG,KAAK,EAAE;EACpB,CAAC,MAAM;IACL,IAAIhB,WAAW,EAAEC,OAAO;IACxB,IAAI,CAACtF,IAAI,CAACY,UAAU,EAAE;MACpByE,WAAW,GAAG,MAAM,IAAAE,0BAAY,EAC9B/E,YAAY,EACZqC,cAAc,CAACD,IAAI,EACnBd,MAAM,EACN9B,IAAI,CACL;MAEDsF,OAAO,GAAGE,YAAY,CACpBhF,YAAY,EACZR,IAAI,CAACsB,IAAI,IAAItB,IAAI,CAACmB,aAAa,EAC/BkE,WAAW,EACXvD,MAAM,CACP;IACH,CAAC,MAAM;MACLwD,OAAO,GAAGtF,IAAI,CAACY,UAAU;IAC3B;IAEA,MAAM0F,oBAAoB,GAAG,CAC3B,UAAU,EACVhB,OAAO,EACP,MAAM,EACNzC,cAAc,CAACD,IAAI,EACnB,cAAc,CACf;IAEDxB,kBAAM,CAACgB,IAAI,CAAE,wCAAuCS,cAAc,CAACjB,IAAK,EAAC,CAAC;IAE1E,MAAM2E,eAAe,GAAGvB,wBAAa,CAACS,SAAS,CAC7C,YAAY,EACZa,oBAAoB,EACpB;MAACpB,QAAQ,EAAE;IAAM,CAAC,CACnB;IAED,IAAIqB,eAAe,CAACjC,KAAK,EAAE;MACzB,MAAM,KAAI/D,oBAAQ,EACf,gGAA+FgG,eAAe,CAACjC,KAAK,CAACkC,OAAQ,EAAC,CAChI;IACH;EACF;EAEA,OAAOpF,kBAAM,CAAC0E,OAAO,CAAC,kCAAkC,CAAC;AAC3D;AAEA,SAASV,aAAa,CAACR,iBAAyB,EAAE;EAChD,MAAM6B,iBAAiB,GAAGC,mBAAmB,CAAC9B,iBAAiB,CAAC;EAChExD,kBAAM,CAACgB,IAAI,CAAE,aAAYqE,iBAAkB,EAAC,CAAC;EAE7CzB,wBAAa,CAACS,SAAS,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAEb,iBAAiB,CAAChC,IAAI,CAAC,CAAC;AAC9E;AAEA,SAAS+D,cAAc,CAACC,aAAqB,EAAE;EAC7C,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,aAAa,CAAC;;EAE1C;EACA,KAAK,MAAMI,CAAC,IAAIH,QAAQ,EAAE;IACxB,MAAMI,gBAAgB,GAAGJ,QAAQ,CAACG,CAAC,CAAC,CAACJ,aAAa,CAACM,iBAAiB;IAEpE,IAAID,gBAAgB,KAAK,KAAK,EAAE;MAC9B,OAAO;QACLE,cAAc,EAAEN,QAAQ,CAACG,CAAC,CAAC,CAACJ,aAAa,CAACQ,gBAAgB;QAC1DC,oBAAoB,EAAER,QAAQ,CAACG,CAAC,CAAC,CAACJ,aAAa,CAACU;MAClD,CAAC;IACH;EACF;EAEA,OAAO,CAAC,CAAC;AACX;AAEA,SAAS9B,YAAY,CACnBhF,YAA4B,EAC5Bc,IAAwB,EACxB+D,WAAmB,EACnBvD,MAAc,EACdyF,UAAmB,GAAG,KAAK,EAC3B;EACA,MAAMX,aAAa,GAAG5B,wBAAa,CAACC,YAAY,CAC9C,YAAY,EACZ,CACEzE,YAAY,CAAC6B,WAAW,GAAG,YAAY,GAAG,UAAU,EACpD7B,YAAY,CAACoB,IAAI,EACjB,SAAS,EACTE,MAAM,EACN,MAAM,EACN0F,eAAe,CAACnC,WAAW,CAAC,EAC5B,gBAAgB,EAChB/D,IAAI,EACJ,oBAAoB,EACpB,OAAO,CACR,EACD;IAAC4D,QAAQ,EAAE;EAAM,CAAC,CACnB;EACD,MAAM;IAACiC,cAAc;IAAEE;EAAoB,CAAC,GAAGV,cAAc,CAACC,aAAa,CAAC;EAE5E,IAAI,CAACO,cAAc,EAAE;IACnB,MAAM,KAAI5G,oBAAQ,EAAC,2CAA2C,CAAC;EACjE;EAEA,IAAI,CAAC8G,oBAAoB,EAAE;IACzB,MAAM,KAAI9G,oBAAQ,EAAC,6BAA6B,CAAC;EACnD;EAEA,OAAQ,GAAE4G,cAAe,GACvBI,UAAU,GAAG,cAAc,GAAG,EAC/B,IAAGF,oBAAqB,EAAC;AAC5B;AAEA,SAASG,eAAe,CAACnC,WAAmB,EAAE;EAC5C;EACA,MAAMoC,iBAAiB,GAAG,qCAAqC,CAACC,IAAI,CAClErC,WAAW,CACZ;EACD,IAAI,CAACoC,iBAAiB,EAAE;IACtB,MAAM,KAAIlH,oBAAQ,EAChB,iIAAiI,CAClI;EACH;EACA,OAAOkH,iBAAiB,CAAC,CAAC,CAAC;AAC7B;AAEA,SAAS9C,cAAc,CACrBhB,OAAsB,EACtBgE,UAAqC,EACrC;EACA,IAAIA,UAAU,KAAK,IAAI,EAAE;IACvB,MAAMC,cAAc,GAAGjE,OAAO,CAACY,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACzB,IAAI,KAAK,QAAQ,CAAE;IAChE,IAAI6E,cAAc,EAAE;MAClBxG,kBAAM,CAACgB,IAAI,CACR,uCAAsCE,gBAAK,CAACC,IAAI,CAC/CqF,cAAc,CAAChG,IAAI,CACnB,iCAAgC,CACnC;MACD,OAAOgG,cAAc;IACvB,CAAC,MAAM;MACLxG,kBAAM,CAACkD,KAAK,CAAC,2BAA2B,CAAC;MACzC,OAAOuD,SAAS;IAClB;EACF;EACA,MAAMC,YAAY,GAAGnE,OAAO,CAACY,IAAI,CAC9B5B,MAAM,IACLA,MAAM,CAACf,IAAI,KAAK+F,UAAU,IAAIjB,mBAAmB,CAAC/D,MAAM,CAAC,KAAKgF,UAAU,CAC3E;EACD,IAAI,CAACG,YAAY,EAAE;IACjB1G,kBAAM,CAACkD,KAAK,CACT,mCAAkChC,gBAAK,CAACC,IAAI,CAC3CwF,MAAM,CAACJ,UAAU,CAAC,CAClB,MAAKlD,iBAAiB,CAACd,OAAO,CAAE,EAAC,CACpC;EACH;EACA,OAAOmE,YAAY;AACrB;AAEA,SAASpB,mBAAmB,CAACxD,SAAiB,EAAE;EAC9C,OAAOA,SAAS,CAAC8E,OAAO,GACnB,GAAE9E,SAAS,CAACtB,IAAK,KAAIsB,SAAS,CAAC8E,OAAQ,GAAE,GAC1C9E,SAAS,CAACtB,IAAI;AACpB;AAEA,SAAS6C,iBAAiB,CAACd,OAAsB,EAAE;EACjD,OAAO,CACL,oBAAoB,EACpB,GAAGA,OAAO,CAACC,GAAG,CAAEjB,MAAM,IAAM,OAAMA,MAAM,CAACf,IAAK,KAAIe,MAAM,CAACC,IAAK,GAAE,CAAC,CAClE,CAAC7B,IAAI,CAAC,IAAI,CAAC;AACd;AAAC,eAEc;EACba,IAAI,EAAE,SAAS;EACfqG,WAAW,EAAE,gDAAgD;EAC7DC,IAAI,EAAErI,MAAM;EACZsI,QAAQ,EAAE,CACR;IACEC,IAAI,EAAE,+DAA+D;IACrEC,GAAG,EAAE;EACP,CAAC,EACD;IACED,IAAI,EAAE,8CAA8C;IACpDC,GAAG,EAAE;EACP,CAAC,EACD;IACED,IAAI,EAAE,8BAA8B;IACpCC,GAAG,EACD;EACJ,CAAC,CACF;EACDC,OAAO,EAAE,CACP,GAAGC,yBAAe,EAClB;IACE3G,IAAI,EAAE,eAAe;IACrBqG,WAAW,EAAE;EACf,CAAC,EACD;IACErG,IAAI,EAAE,wBAAwB;IAC9BqG,WAAW,EACT;EACJ,CAAC,EACD;IACErG,IAAI,EAAE,gBAAgB;IACtBqG,WAAW,EACT;EACJ,CAAC,EACD;IACErG,IAAI,EAAE,eAAe;IACrBqG,WAAW,EACT;EACJ,CAAC;AAEL,CAAC;AAAA"}
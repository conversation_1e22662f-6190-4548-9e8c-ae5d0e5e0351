{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017", "es2015", "es6"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "resolveJsonModule": true, "skipLibCheck": true, "declaration": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*", "App.tsx", "index.js"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js", "android", "ios"]}
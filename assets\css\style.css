/* ===== VARIABLES CSS ===== */
:root {
  /* Colors */
  --primary-color: #1e40af;
  --primary-light: #3b82f6;
  --accent-color: #f97316;
  --white-color: #ffffff;
  --gray-color: #64748b;
  --gray-dark: #334155;
  --black-color: #0f172a;
  --gradient-primary: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  --gradient-accent: linear-gradient(135deg, #f97316 0%, #fb923c 100%);
  
  /* Typography */
  --body-font: 'Inter', sans-serif;
  --heading-font: 'Poppins', sans-serif;
  --biggest-font-size: 3rem;
  --h1-font-size: 2.5rem;
  --h2-font-size: 2rem;
  --h3-font-size: 1.5rem;
  --normal-font-size: 1rem;
  --small-font-size: 0.875rem;
  --smaller-font-size: 0.75rem;
  
  /* Font Weight */
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semi-bold: 600;
  --font-bold: 700;
  
  /* Margins */
  --mb-0-25: 0.25rem;
  --mb-0-5: 0.5rem;
  --mb-0-75: 0.75rem;
  --mb-1: 1rem;
  --mb-1-5: 1.5rem;
  --mb-2: 2rem;
  --mb-2-5: 2.5rem;
  --mb-3: 3rem;
  
  /* Z-index */
  --z-tooltip: 10;
  --z-fixed: 100;
  --z-modal: 1000;
  
  /* Transitions */
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;
  
  /* Shadows */
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
  
  /* Border Radius */
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
}

/* ===== BASE ===== */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
  background-color: var(--white-color);
  color: var(--gray-dark);
  line-height: 1.6;
}

h1, h2, h3, h4 {
  color: var(--gray-dark);
  font-family: var(--heading-font);
  font-weight: var(--font-semi-bold);
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
  color: inherit;
}

img {
  max-width: 100%;
  height: auto;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
}

/* ===== REUSABLE CSS CLASSES ===== */
.container {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.section {
  padding: 5rem 0 2rem;
}

.section__header {
  text-align: center;
  margin-bottom: var(--mb-3);
}

.section__title {
  font-size: var(--h2-font-size);
  color: var(--gray-dark);
  margin-bottom: var(--mb-0-75);
  position: relative;
}

.section__title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--gradient-accent);
  border-radius: 2px;
}

.section__subtitle {
  font-size: var(--normal-font-size);
  color: var(--gray-color);
  max-width: 600px;
  margin: 0 auto;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  font-weight: var(--font-medium);
  font-size: var(--normal-font-size);
  transition: var(--transition);
  cursor: pointer;
  text-align: center;
  border: 2px solid transparent;
}

.btn--primary {
  background: var(--gradient-primary);
  color: var(--white-color);
  box-shadow: var(--shadow-medium);
}

.btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.btn--secondary {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn--secondary:hover {
  background: var(--primary-color);
  color: var(--white-color);
}

.btn--outline {
  background: transparent;
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn--outline:hover {
  background: var(--accent-color);
  color: var(--white-color);
}

/* ===== HEADER & NAV ===== */
.header {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-fixed);
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: var(--transition);
}

.scroll-header {
  box-shadow: var(--shadow-light);
  background-color: rgba(255, 255, 255, 0.98);
}

.nav {
  height: 4.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav__logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: var(--font-bold);
  color: var(--primary-color);
}

.nav__logo-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: contain;
}

.nav__logo-text {
  font-size: 1.25rem;
  font-family: var(--heading-font);
}

.nav__menu {
  position: relative;
}

.nav__list {
  display: flex;
  gap: 2rem;
}

.nav__link {
  color: var(--gray-dark);
  font-weight: var(--font-medium);
  transition: var(--transition);
  position: relative;
}

.nav__link:hover,
.nav__link.active-link {
  color: var(--primary-color);
}

.nav__link::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-accent);
  transition: var(--transition);
}

.nav__link:hover::after,
.nav__link.active-link::after {
  width: 100%;
}

.nav__toggle,
.nav__close {
  display: none;
  font-size: 1.25rem;
  color: var(--gray-dark);
  cursor: pointer;
}

/* ===== HERO ===== */
.hero {
  padding-top: 7rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0.05;
  transform: skewX(-15deg);
  transform-origin: top;
}

.hero__container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero__content {
  animation: fadeInUp 1s ease-out;
}

.hero__title {
  font-size: var(--biggest-font-size);
  font-weight: var(--font-bold);
  line-height: 1.2;
  margin-bottom: var(--mb-1);
}

.hero__title-accent {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero__description {
  font-size: 1.125rem;
  color: var(--gray-color);
  margin-bottom: var(--mb-2-5);
  line-height: 1.7;
}

.hero__buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero__image {
  position: relative;
  animation: fadeInRight 1s ease-out 0.3s both;
}

.hero__img {
  width: 100%;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-heavy);
}

.hero__scroll {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.hero__scroll-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--white-color);
  border-radius: 50%;
  color: var(--primary-color);
  box-shadow: var(--shadow-medium);
  transition: var(--transition);
}

.hero__scroll-link:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

/* ===== ABOUT ===== */
.about {
  background: var(--white-color);
}

.about__container {
  display: grid;
  gap: 3rem;
}

.about__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.about__title {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-1);
  color: var(--primary-color);
}

.about__description {
  color: var(--gray-color);
  margin-bottom: var(--mb-1-5);
  line-height: 1.7;
}

.about__values {
  display: grid;
  gap: 1.5rem;
}

.about__value {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.about__value-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: var(--gradient-primary);
  color: var(--white-color);
  border-radius: var(--border-radius);
  font-size: 1.25rem;
  flex-shrink: 0;
}

.about__value-title {
  font-size: 1.125rem;
  margin-bottom: var(--mb-0-25);
  color: var(--gray-dark);
}

.about__value-description {
  color: var(--gray-color);
  font-size: var(--small-font-size);
}

.about__stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  text-align: center;
}

.about__stat {
  padding: 2rem 1rem;
  background: var(--white-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.about__stat:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.about__stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: var(--font-bold);
  color: var(--primary-color);
  margin-bottom: var(--mb-0-5);
}

.about__stat-text {
  color: var(--gray-color);
  font-size: var(--small-font-size);
  font-weight: var(--font-medium);
}

/* ===== SERVICES ===== */
.services {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.services__container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.service__card {
  background: var(--white-color);
  padding: 2.5rem 2rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  text-align: center;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.service__card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--gradient-accent);
}

.service__card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-heavy);
}

.service__icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  color: var(--white-color);
  border-radius: 50%;
  font-size: 2rem;
  margin-bottom: var(--mb-1-5);
}

.service__title {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-1-5);
  color: var(--gray-dark);
}

.service__list {
  margin-bottom: var(--mb-2);
  text-align: left;
}

.service__item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: var(--mb-0-75);
  color: var(--gray-color);
}

.service__item i {
  color: var(--accent-color);
  font-size: var(--small-font-size);
}

/* ===== WHATSAPP FLOAT ===== */
.whatsapp-float {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: var(--z-tooltip);
}

.whatsapp-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: #25d366;
  color: var(--white-color);
  border-radius: 50%;
  font-size: 1.5rem;
  box-shadow: var(--shadow-heavy);
  transition: var(--transition);
  animation: pulse 2s infinite;
}

.whatsapp-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 32px rgba(37, 211, 102, 0.4);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) translateX(-50%);
  }
  40% {
    transform: translateY(-10px) translateX(-50%);
  }
  60% {
    transform: translateY(-5px) translateX(-50%);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
  }
}

/* ===== PROJECTS ===== */
.projects {
  background: var(--white-color);
}

.projects__filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: var(--mb-3);
  flex-wrap: wrap;
}

.filter__btn {
  padding: 0.75rem 1.5rem;
  background: transparent;
  color: var(--gray-color);
  border: 2px solid var(--gray-color);
  border-radius: var(--border-radius);
  font-weight: var(--font-medium);
  transition: var(--transition);
  cursor: pointer;
}

.filter__btn:hover,
.filter__btn.active {
  background: var(--primary-color);
  color: var(--white-color);
  border-color: var(--primary-color);
}

.projects__container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.project__card {
  position: relative;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.project__card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-heavy);
}

.project__image {
  position: relative;
  overflow: hidden;
}

.project__img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: var(--transition);
}

.project__card:hover .project__img {
  transform: scale(1.1);
}

.project__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(30, 64, 175, 0.9);
  color: var(--white-color);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  opacity: 0;
  transition: var(--transition);
}

.project__card:hover .project__overlay {
  opacity: 1;
}

.project__title {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-0-5);
}

.project__description {
  margin-bottom: var(--mb-1-5);
  font-size: var(--small-font-size);
}

.project__btn {
  background: var(--accent-color);
  color: var(--white-color);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: var(--font-medium);
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.project__btn:hover {
  background: #e85d04;
  transform: translateY(-2px);
}

/* ===== MODAL ===== */
.modal {
  display: none;
  position: fixed;
  z-index: var(--z-modal);
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  animation: fadeIn 0.3s ease;
}

.modal__content {
  background-color: var(--white-color);
  margin: 5% auto;
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 600px;
  position: relative;
  animation: slideIn 0.3s ease;
}

.modal__close {
  position: absolute;
  top: 1rem;
  right: 1.5rem;
  color: var(--gray-color);
  font-size: 2rem;
  font-weight: bold;
  cursor: pointer;
  transition: var(--transition);
}

.modal__close:hover {
  color: var(--primary-color);
}

.modal__title {
  font-size: var(--h2-font-size);
  margin-bottom: var(--mb-1);
  color: var(--primary-color);
}

.modal__image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: var(--border-radius);
  margin-bottom: var(--mb-1-5);
}

.modal__description {
  color: var(--gray-color);
  line-height: 1.7;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== SCHEDULE ===== */
.schedule {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.schedule__container {
  max-width: 1000px;
  margin: 0 auto;
}

.schedule__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.schedule__title {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-1-5);
  color: var(--primary-color);
}

.schedule__list {
  display: grid;
  gap: 1rem;
}

.schedule__item {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--gray-color);
}

.schedule__item i {
  color: var(--accent-color);
  font-size: 1.25rem;
}

.schedule__form {
  background: var(--white-color);
  padding: 2.5rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-medium);
}

.form__group {
  margin-bottom: var(--mb-1-5);
}

.form__row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form__label {
  display: block;
  margin-bottom: var(--mb-0-5);
  color: var(--gray-dark);
  font-weight: var(--font-medium);
}

.form__input,
.form__select,
.form__textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: var(--border-radius);
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
  transition: var(--transition);
  background: var(--white-color);
}

.form__input:focus,
.form__select:focus,
.form__textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.form__textarea {
  resize: vertical;
  min-height: 120px;
}

.form__submit {
  width: 100%;
  justify-content: center;
  margin-top: var(--mb-1);
}

/* ===== CONTACT ===== */
.contact {
  background: var(--white-color);
}

.contact__container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact__info {
  display: grid;
  gap: 2rem;
}

.contact__card {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
  padding: 2rem;
  background: var(--white-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.contact__card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.contact__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  color: var(--white-color);
  border-radius: var(--border-radius);
  font-size: 1.5rem;
  flex-shrink: 0;
}

.contact__title {
  font-size: 1.125rem;
  margin-bottom: var(--mb-0-5);
  color: var(--gray-dark);
}

.contact__description {
  color: var(--gray-color);
  line-height: 1.6;
}

.contact__description a {
  color: var(--primary-color);
  transition: var(--transition);
}

.contact__description a:hover {
  color: var(--accent-color);
}

.contact__map {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.contact__iframe {
  border-radius: var(--border-radius-lg);
}

/* ===== FOOTER ===== */
.footer {
  background: var(--gray-dark);
  color: var(--white-color);
  padding: 3rem 0 1rem;
}

.footer__container {
  display: grid;
  gap: 2rem;
}

.footer__content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 3rem;
}

.footer__logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: var(--mb-1);
}

.footer__logo-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.footer__logo-text {
  font-size: 1.5rem;
  font-weight: var(--font-bold);
  font-family: var(--heading-font);
  color: var(--white-color);
}

.footer__description {
  color: #94a3b8;
  margin-bottom: var(--mb-1-5);
  line-height: 1.6;
}

.footer__social {
  display: flex;
  gap: 1rem;
}

.footer__social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--white-color);
  border-radius: 50%;
  transition: var(--transition);
}

.footer__social-link:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
}

.footer__title {
  font-size: 1.125rem;
  margin-bottom: var(--mb-1-5);
  color: var(--white-color);
}

.footer__list {
  display: grid;
  gap: 0.75rem;
}

.footer__item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer__item i {
  color: var(--accent-color);
  font-size: var(--small-font-size);
}

.footer__link {
  color: #94a3b8;
  transition: var(--transition);
}

.footer__link:hover {
  color: var(--white-color);
}

.footer__bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 2rem;
}

.footer__copy {
  color: #94a3b8;
  font-size: var(--small-font-size);
}

.footer__certificates {
  display: flex;
  gap: 2rem;
}

.footer__certificate {
  color: #94a3b8;
  font-size: var(--smaller-font-size);
}

/* ===== RESPONSIVE DESIGN ===== */

/* For large devices */
@media screen and (max-width: 1200px) {
  .container {
    max-width: 968px;
  }

  .hero__container {
    gap: 2rem;
  }
}

/* For medium devices */
@media screen and (max-width: 968px) {
  .container {
    margin-left: var(--mb-1-5);
    margin-right: var(--mb-1-5);
  }

  .section {
    padding: 4rem 0 2rem;
  }

  .hero__container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero__content {
    order: 2;
  }

  .hero__image {
    order: 1;
  }

  .about__content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about__stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .schedule__content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact__container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer__content {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
}

/* For small devices */
@media screen and (max-width: 768px) {
  :root {
    --biggest-font-size: 2.25rem;
    --h1-font-size: 2rem;
    --h2-font-size: 1.5rem;
    --h3-font-size: 1.25rem;
    --normal-font-size: 0.938rem;
    --small-font-size: 0.813rem;
    --smaller-font-size: 0.75rem;
  }

  .container {
    margin-left: var(--mb-1);
    margin-right: var(--mb-1);
  }

  .nav__menu {
    position: fixed;
    top: -100%;
    left: 0;
    width: 100%;
    background-color: var(--white-color);
    padding: 4rem 0 3rem;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
    z-index: var(--z-fixed);
  }

  .nav__menu.show-menu {
    top: 0;
  }

  .nav__list {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }

  .nav__link {
    font-size: 1.125rem;
  }

  .nav__close,
  .nav__toggle {
    display: block;
  }

  .nav__close {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    font-size: 1.5rem;
  }

  .hero {
    padding-top: 6rem;
  }

  .hero__buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .about__stats {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .services__container {
    grid-template-columns: 1fr;
  }

  .projects__container {
    grid-template-columns: 1fr;
  }

  .projects__filters {
    flex-direction: column;
    align-items: center;
  }

  .filter__btn {
    width: 100%;
    max-width: 200px;
  }

  .form__row {
    grid-template-columns: 1fr;
  }

  .contact__info {
    gap: 1.5rem;
  }

  .contact__card {
    padding: 1.5rem;
  }

  .footer__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .footer__bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer__certificates {
    flex-direction: column;
    gap: 0.5rem;
  }

  .whatsapp-float {
    bottom: 1rem;
    right: 1rem;
  }

  .whatsapp-btn {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
}

/* For extra small devices */
@media screen and (max-width: 320px) {
  .container {
    margin-left: var(--mb-0-75);
    margin-right: var(--mb-0-75);
  }

  .hero__title {
    font-size: 2rem;
  }

  .section__title {
    font-size: 1.5rem;
  }

  .service__card,
  .schedule__form {
    padding: 1.5rem;
  }

  .modal__content {
    margin: 10% auto;
    padding: 1.5rem;
    width: 95%;
  }
}

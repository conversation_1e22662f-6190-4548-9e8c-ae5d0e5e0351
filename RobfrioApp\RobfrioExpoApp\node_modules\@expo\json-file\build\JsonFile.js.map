{"version": 3, "file": "JsonFile.js", "sourceRoot": "", "sources": ["../src/JsonFile.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAqD;AACrD,4CAAoB;AACpB,kDAA0B;AAC1B,gDAAwB;AACxB,+BAAiC;AACjC,0EAAgD;AAEhD,iEAAoE;AAEpE,MAAM,oBAAoB,GAId,IAAA,gBAAS,EAAC,2BAAe,CAAC,CAAC;AAqBvC,MAAM,eAAe,GAAG;IACtB,cAAc,EAAE,SAAS;IACzB,qBAAqB,EAAE,SAAS;IAChC,mBAAmB,EAAE,SAAS;IAC9B,SAAS,EAAE,KAAK;IAChB,OAAO,EAAE,SAAS;IAClB,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,CAAC;IACR,eAAe,EAAE,IAAI;CACtB,CAAC;AAEF;;;;;;GAMG;AACH,MAAqB,QAAQ;IAC3B,IAAI,CAAS;IACb,OAAO,CAAuB;IAE9B,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,MAAM,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,MAAM,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,MAAM,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC;IAEnC,YAAY,IAAY,EAAE,UAAgC,EAAE;QAC1D,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,IAAI,CAAC,OAA8B;QACjC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAA8B;QAC5C,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAmB,EAAE,OAA8B;QAClE,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,eAAe,CAAC,IAAY,EAAE,OAA8B;QAC1D,OAAO,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,GAAM,EACN,YAAsB,EACtB,OAA8B;QAE9B,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,GAAW,EAAE,KAAc,EAAE,OAA8B;QACxE,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,UAAU,CACd,OAAsD,EACtD,OAA8B;QAE9B,OAAO,UAAU,CAAc,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAChF,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,GAAW,EAAE,OAA8B;QAC9D,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,IAAc,EAAE,OAA8B;QAClE,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IACrE,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,OAA8B;QAC/C,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,WAAW,CAAC,OAA8B;QACxC,OAAO;YACL,GAAG,IAAI,CAAC,OAAO;YACf,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;;AAxEH,2BAyEC;AAED,SAAS,IAAI,CACX,IAAY,EACZ,OAA8B;IAE9B,IAAI,IAAI,CAAC;IACT,IAAI;QACF,IAAI,GAAG,YAAE,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACtC;IAAC,OAAO,KAAU,EAAE;QACnB,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,MAAM,YAAY,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,MAAM,IAAI,uBAAa,CAAC,yBAAyB,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACnF;aAAM;YACL,OAAO,YAAY,CAAC;SACrB;KACF;IACD,OAAO,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC9C,CAAC;AAED,KAAK,UAAU,SAAS,CACtB,IAAY,EACZ,OAA8B;IAE9B,IAAI,IAAI,CAAC;IACT,IAAI;QACF,IAAI,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACjD;IAAC,OAAO,KAAU,EAAE;QACnB,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,MAAM,YAAY,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,MAAM,IAAI,uBAAa,CAAC,yBAAyB,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SAC7E;aAAM;YACL,OAAO,YAAY,CAAC;SACrB;KACF;IACD,OAAO,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,eAAe,CACtB,IAAY,EACZ,OAA8B,EAC9B,QAAiB;IAEjB,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtC,IAAI;QACF,IAAI,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YAChC,OAAO,eAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC1B;aAAM;YACL,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACzB;KACF;IAAC,OAAO,CAAM,EAAE;QACf,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,MAAM,QAAQ,GAAG,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAClD,IAAI,QAAQ,EAAE;gBACZ,MAAM,SAAS,GAAG,IAAA,6BAAgB,EAAC,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAC9D,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC;gBACxB,CAAC,CAAC,OAAO,IAAI,KAAK,SAAS,EAAE,CAAC;aAC/B;YACD,MAAM,IAAI,uBAAa,CAAC,uBAAuB,IAAI,EAAE,EAAE,CAAC,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;SACnF;aAAM;YACL,OAAO,YAAY,CAAC;SACrB;KACF;AACH,CAAC;AAED,KAAK,UAAU,QAAQ,CACrB,IAAY,EACZ,GAAM,EACN,YAA0B,EAC1B,OAA8B;IAE9B,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,IAAI,GAAG,IAAI,MAAM,EAAE;QACjB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;KACpB;IACD,IAAI,YAAY,KAAK,SAAS,EAAE;QAC9B,MAAM,IAAI,uBAAa,CAAC,yBAAyB,MAAM,CAAC,GAAG,CAAC,0BAA0B,IAAI,EAAE,CAAC,CAAC;KAC/F;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,KAAK,UAAU,UAAU,CACvB,IAAY,EACZ,MAAmB,EACnB,OAA8B;IAE9B,IAAI,OAAO,EAAE,SAAS,EAAE;QACtB,MAAM,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;KAClE;IACD,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3C,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3C,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC;IACT,IAAI;QACF,IAAI,KAAK,EAAE;YACT,IAAI,GAAG,eAAK,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SAC7C;aAAM;YACL,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SAC5C;KACF;IAAC,OAAO,CAAM,EAAE;QACf,MAAM,IAAI,uBAAa,CAAC,4CAA4C,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;KAChF;IACD,MAAM,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAClD,MAAM,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IAC3C,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,QAAQ,CACrB,IAAY,EACZ,GAAW,EACX,KAAc,EACd,OAA8B;IAE9B,kEAAkE;IAClE,oEAAoE;IACpE,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,OAAO,UAAU,CAAC,IAAI,EAAE,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC;AAED,KAAK,UAAU,UAAU,CACvB,IAAY,EACZ,OAAsD,EACtD,OAA8B;IAE9B,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAC1B,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC;KACnC;SAAM;QACL,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KAChC;IACD,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC3C,CAAC;AAED,KAAK,UAAU,cAAc,CAC3B,IAAY,EACZ,GAAW,EACX,OAA8B;IAE9B,OAAO,eAAe,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AAC/C,CAAC;AAED,KAAK,UAAU,eAAe,CAC5B,IAAY,EACZ,IAAc,EACd,OAA8B;IAE9B,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,IAAI,SAAS,GAAG,KAAK,CAAC;IAEtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC9B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;YACnB,SAAS,GAAG,IAAI,CAAC;SAClB;KACF;IAED,IAAI,SAAS,EAAE;QACb,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;KAC1C;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,YAAY,CACzB,IAAY,EACZ,OAA8B;IAE9B,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,qBAAqB,CAC5B,UAAgC,EAAE;IAElC,IAAI,OAAO,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAC/C,OAAO,OAAO,CAAC,OAAO,CAAC;KACxB;SAAM;QACL,OAAO,OAAO,CAAC,qBAAqB,CAAC;KACtC;AACH,CAAC;AAED,SAAS,mBAAmB,CAC1B,UAAgC,EAAE;IAElC,IAAI,OAAO,CAAC,mBAAmB,KAAK,SAAS,EAAE;QAC7C,OAAO,OAAO,CAAC,OAAO,CAAC;KACxB;SAAM;QACL,OAAO,OAAO,CAAC,mBAAmB,CAAC;KACpC;AACH,CAAC;AAED,SAAS,UAAU,CACjB,OAAyC,EACzC,KAAQ;IAER,IAAI,OAAO,EAAE;QACX,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;YAChC,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;SACvB;KACF;IACD,OAAO,eAAe,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC;AAED,SAAS,uBAAuB,CAAC,KAAU,EAAE,YAAoB;IAC/D,qDAAqD;IACrD,IAAI,YAAY,IAAI,KAAK,IAAI,cAAc,IAAI,KAAK,EAAE;QACpD,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC;KAC/D;IACD,2DAA2D;IAC3D,MAAM,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACtD,IAAI,KAAK,EAAE;QACT,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3D,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;KACvE;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,qBAAqB,CAAC,IAAa,EAAE,IAAa;IACzD,IAAI,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QACvB,MAAM,IAAI,kCAAkB,CAAC,IAAI,CAAC,CAAC;KACpC;AACH,CAAC"}
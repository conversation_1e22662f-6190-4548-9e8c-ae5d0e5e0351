export interface PixelRatioStatic {
    /*
          Returns the device pixel density. Some examples:
              PixelRatio.get() === 1
              mdpi Android devices (160 dpi)
              PixelRatio.get() === 1.5
              hdpi Android devices (240 dpi)
              PixelRatio.get() === 2
              iPhone 4, 4S
              iPhone 5, 5c, 5s
              iPhone 6
              xhdpi Android devices (320 dpi)
              PixelRatio.get() === 3
              iPhone 6 plus
              xxhdpi Android devices (480 dpi)
              PixelRatio.get() === 3.5
              Nexus 6
      */
    get(): number;

    /*
          Returns the scaling factor for font sizes. This is the ratio that is
          used to calculate the absolute font size, so any elements that
          heavily depend on that should use this to do calculations.

          If a font scale is not set, this returns the device pixel ratio.

          Currently this is only implemented on Android and reflects the user
          preference set in Settings > Display > Font size,
          on iOS it will always return the default pixel ratio.
          */
    getFontScale(): number;

    /**
     * Converts a layout size (dp) to pixel size (px).
     * Guaranteed to return an integer number.
     */
    getPixelSizeForLayoutSize(layoutSize: number): number;

    /**
     * Rounds a layout size (dp) to the nearest layout size that
     * corresponds to an integer number of pixels. For example,
     * on a device with a PixelRatio of 3,
     * PixelRatio.roundToNearestPixel(8.4) = 8.33,
     * which corresponds to exactly (8.33 * 3) = 25 pixels.
     */
    roundToNearestPixel(layoutSize: number): number;

    /**
     * No-op for iOS, but used on the web. Should not be documented. [sic]
     */
    startDetecting(): void;
}

export const PixelRatio: PixelRatioStatic;

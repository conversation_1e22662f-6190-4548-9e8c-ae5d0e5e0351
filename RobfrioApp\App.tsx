/**
 * ROBFRIO Mobile App - VERSÃO PREMIUM
 * Aplicativo de Refrigeração Comercial
 *
 * Features:
 * - Navigation com Stack + Bottom Tabs
 * - Error Boundary para captura de erros
 * - Loading state management
 * - Performance optimization
 * - Offline support
 */

import React, {useEffect, useState} from 'react';
import {
  StatusBar,
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DeviceInfo from 'react-native-device-info';
import 'react-native-gesture-handler';

// Navigation
import BottomTabNavigator from './src/navigation/BottomTabNavigator';

// Error Boundary Component
class ErrorBoundary extends React.Component<
  {children: React.ReactNode},
  {hasError: boolean; error?: Error}
> {
  constructor(props: {children: React.ReactNode}) {
    super(props);
    this.state = {hasError: false};
  }

  static getDerivedStateFromError(error: Error) {
    return {hasError: true, error};
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('App Error:', error, errorInfo);
    // Aqui você pode enviar o erro para um serviço de monitoramento
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Ops! Algo deu errado</Text>
          <Text style={styles.errorMessage}>
            O aplicativo encontrou um erro inesperado.
          </Text>
          <Text style={styles.errorDetails}>
            {this.state.error?.message}
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

// Loading Component
const LoadingScreen: React.FC = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#1e40af" />
    <Text style={styles.loadingText}>Carregando ROBFRIO...</Text>
  </View>
);

const App: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [appInfo, setAppInfo] = useState<{
    version: string;
    buildNumber: string;
    deviceId: string;
  } | null>(null);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Simular carregamento inicial
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Obter informações do dispositivo
      const version = DeviceInfo.getVersion();
      const buildNumber = DeviceInfo.getBuildNumber();
      const deviceId = await DeviceInfo.getUniqueId();

      setAppInfo({version, buildNumber, deviceId});

      // Verificar se é a primeira execução
      const isFirstLaunch = await AsyncStorage.getItem('isFirstLaunch');
      if (!isFirstLaunch) {
        await AsyncStorage.setItem('isFirstLaunch', 'false');
        // Mostrar tutorial ou onboarding
      }

      // Verificar conectividade e sincronizar dados offline
      await syncOfflineData();

      setIsLoading(false);
    } catch (error) {
      console.error('Erro na inicialização:', error);
      Alert.alert(
        'Erro de Inicialização',
        'Houve um problema ao inicializar o aplicativo. Tente novamente.',
        [
          {
            text: 'Tentar Novamente',
            onPress: initializeApp,
          },
        ],
      );
    }
  };

  const syncOfflineData = async () => {
    try {
      // Implementar sincronização de dados offline
      const cachedData = await AsyncStorage.getItem('cachedAppData');
      if (cachedData) {
        // Processar dados em cache
        console.log('Dados em cache carregados');
      }
    } catch (error) {
      console.error('Erro na sincronização offline:', error);
    }
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <NavigationContainer>
          <StatusBar
            barStyle="light-content"
            backgroundColor="#1e40af"
            translucent={false}
          />
          <BottomTabNavigator />
        </NavigationContainer>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  loadingText: {
    marginTop: 20,
    fontSize: 16,
    color: '#1e40af',
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 20,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#dc2626',
    marginBottom: 10,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 20,
  },
  errorDetails: {
    fontSize: 12,
    color: '#94a3b8',
    textAlign: 'center',
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
});

export default App;

/**
 * ROBFRIO Mobile App - VERSÃO SIMPLIFICADA
 * Aplicativo de Refrigeração Comercial
 */

import React from 'react';
import {StatusBar} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import 'react-native-gesture-handler';

// Navigation
import BottomTabNavigator from './src/navigation/BottomTabNavigator';

const App: React.FC = () => {
  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <StatusBar
          barStyle="light-content"
          backgroundColor="#1e40af"
          translucent={false}
        />
        <BottomTabNavigator />
      </NavigationContainer>
    </SafeAreaProvider>
  );
};

export default App;

var e=require("graphql"),r=require("wonka");function t(r){return"string"==typeof r?new e.GraphQLError(r):"object"==typeof r&&r.message?new e.GraphQLError(r.message,r.nodes,r.source,r.positions,r.path,r,r.extensions||{}):r}var n=function(e){function r(r){var n,o,a,i=r.networkError,s=r.response,u=(r.graphQLErrors||[]).map(t),c=(o=u,a="",void 0!==(n=i)?a="[Network] "+n.message:(void 0!==o&&o.forEach((function(e){a+="[GraphQL] "+e.message+"\n"})),a.trim()));e.call(this,c),this.name="CombinedError",this.message=c,this.graphQLErrors=u,this.networkError=i,this.response=s}return e&&(r.__proto__=e),(r.prototype=Object.create(e&&e.prototype)).constructor=r,r.prototype.toString=function(){return this.message},r}(Error);function o(e,r){e|=0;for(var t=0,n=0|r.length;t<n;t++)e=(e<<5)+e+r.charCodeAt(t);return e}function a(e){return o(5381,e)>>>0}var i=new Set,s=new WeakMap;function u(e){if(null===e||i.has(e))return"null";if("object"!=typeof e)return JSON.stringify(e)||"";if(e.toJSON)return u(e.toJSON());if(Array.isArray(e)){for(var r="[",t=0,n=e.length;t<n;t++){t>0&&(r+=",");var o=u(e[t]);r+=o.length>0?o:"null"}return r+"]"}var a=Object.keys(e).sort();if(!a.length&&e.constructor&&e.constructor!==Object){var c=s.get(e)||Math.random().toString(36).slice(2);return s.set(e,c),'{"__key":"'+c+'"}'}i.add(e);for(var f="{",p=0,l=a.length;p<l;p++){var d=a[p],h=u(e[d]);h&&(f.length>1&&(f+=","),f+=u(d)+":"+h)}return i.delete(e),f+"}"}function c(e){return i.clear(),u(e)}function f(r){var t=("string"!=typeof r?r.loc&&r.loc.source.body||e.print(r):r).replace(/([\s,]|#[^\n\r]+)+/g," ").trim();if("string"!=typeof r){var n="definitions"in r&&d(r);n&&(t="# "+n+"\n"+t),r.loc||(r.loc={start:0,end:t.length,source:{body:t,name:"gql",locationOffset:{line:1,column:1}}})}return t}var p=new Map;function l(r){var t,n;return"string"==typeof r?(t=a(f(r)),n=p.get(t)||e.parse(r,{noLocation:!0})):(t=r.__key||a(f(r)),n=p.get(t)||r),n.loc||f(n),n.__key=t,p.set(t,n),n}function d(r){for(var t=0,n=r.definitions.length;t<n;t++){var o=r.definitions[t];if(o.kind===e.Kind.OPERATION_DEFINITION&&o.name)return o.name.value}}function h(){return(h=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function v(e,r,t){if(!("data"in r)&&!("errors"in r)||"path"in r)throw new Error("No Content");return{operation:e,data:r.data,error:Array.isArray(r.errors)?new n({graphQLErrors:r.errors,response:t}):void 0,extensions:"object"==typeof r.extensions&&r.extensions||void 0,hasNext:!!r.hasNext}}function y(e,r,t){var o=h({},e);if(o.hasNext=!!r.hasNext,!("path"in r))return"data"in r&&(o.data=r.data),o;Array.isArray(r.errors)&&(o.error=new n({graphQLErrors:o.error?o.error.graphQLErrors.concat(r.errors):r.errors,response:t}));for(var a,i=o.data=h({},o.data),s=0;s<r.path.length;)i=i[a=r.path[s++]]=Array.isArray(i[a])?[].concat(i[a]):h({},i[a]);return h(i,r.data),o}function g(e,r,t){return{operation:e,data:void 0,error:new n({networkError:r,response:t}),extensions:void 0}}function m(e){return"query"===e.kind&&!!e.context.preferGetMethod}var x="undefined"!=typeof Symbol?Symbol.asyncIterator:null,b="undefined"!=typeof TextDecoder?new TextDecoder:null,O=/content-type:[^\r\n]*application\/json/i,k=/boundary="?([^=";]+)"?/i;exports.CombinedError=n,exports._extends=h,exports.createRequest=function(e,r){r||(r={});var t=l(e);return{key:o(t.__key,c(r))>>>0,query:t,variables:r}},exports.getOperationName=d,exports.getOperationType=function(r){for(var t=0,n=r.definitions.length;t<n;t++){var o=r.definitions[t];if(o.kind===e.Kind.OPERATION_DEFINITION)return o.operation}},exports.keyDocument=l,exports.makeErrorResult=g,exports.makeFetchBody=function(r){return{query:e.print(r.query),operationName:d(r.query),variables:r.variables||void 0,extensions:void 0}},exports.makeFetchOptions=function(e,r){var t=m(e),n="function"==typeof e.context.fetchOptions?e.context.fetchOptions():e.context.fetchOptions||{};return h({},n,{body:!t&&r?JSON.stringify(r):void 0,method:t?"GET":"POST",headers:t?n.headers:h({},{"content-type":"application/json"},n.headers)})},exports.makeFetchSource=function(e,t,n){var o="manual"===n.redirect?400:300,a=e.context.fetch;return r.make((function(r){var i=r.next,s=r.complete,u="undefined"!=typeof AbortController?new AbortController:null;u&&(n.signal=u.signal);var c,f=!1,p=!1;return Promise.resolve().then((function(){if(!f)return(a||fetch)(t,n)})).then((function(r){if(r)return p=(c=r).status<200||c.status>=o,function(e,r,t){var n=t.headers&&t.headers.get("Content-Type")||"";if(!/multipart\/mixed/i.test(n))return t.json().then((function(n){e(v(r,n,t))}));var o,a="---",i=n.match(k);i&&(a="--"+i[1]);var s=function(){};if(x&&t[x]){var u=t[x]();o=u.next.bind(u)}else{if(!("body"in t)||!t.body)throw new TypeError("Streaming requests unsupported");var c=t.body.getReader();s=c.cancel.bind(c),o=c.read.bind(c)}var f="",p=!0,l=null,d=null;return o().then((function n(i){if(!i.done){var s="Buffer"===(E=i.value).constructor.name?E.toString():b.decode(E),u=s.indexOf(a);for(u>-1?u+=f.length:u=f.indexOf(a),f+=s;u>-1;){var c=f.slice(0,u),h=f.slice(u+a.length);if(p)p=!1;else{var g=c.indexOf("\r\n\r\n")+4,m=c.slice(0,g),x=c.slice(g,c.lastIndexOf("\r\n")),k=void 0;if(O.test(m))try{k=JSON.parse(x),l=d=d?y(d,k,t):v(r,k,t)}catch(e){}if("--"===h.slice(0,2)||k&&!k.hasNext){if(!d)return e(v(r,{},t));break}}u=(f=h).indexOf(a)}}var E;if(l&&(e(l),l=null),!i.done&&(!d||d.hasNext))return o().then(n)})).finally(s)}(i,e,c)})).then(s).catch((function(r){if("AbortError"!==r.name){var t=g(e,p?new Error(c.statusText):r,c);i(t),s()}})),function(){f=!0,u&&u.abort()}}))},exports.makeFetchURL=function(e,r){var t=m(e),n=e.context.url;if(!t||!r)return n;var o=[];return r.operationName&&o.push("operationName="+encodeURIComponent(r.operationName)),r.query&&o.push("query="+encodeURIComponent(r.query.replace(/#[^\n\r]+/g," ").trim())),r.variables&&o.push("variables="+encodeURIComponent(c(r.variables))),r.extensions&&o.push("extensions="+encodeURIComponent(c(r.extensions))),n+"?"+o.join("&")},exports.makeResult=v,exports.mergeResultPatch=y,exports.stringifyDocument=f,exports.stringifyVariables=c;
//# sourceMappingURL=d354e5b3.min.js.map

{"version": 3, "names": ["global", "REACT_NAVIGATION_DEVTOOLS", "WeakMap", "NavigationContainerInner", "ref", "theme", "DefaultTheme", "linking", "fallback", "documentTitle", "onReady", "rest", "isLinkingEnabled", "enabled", "config", "validatePathConfig", "ref<PERSON><PERSON><PERSON>", "React", "useRef", "useBackButton", "useDocumentTitle", "getInitialState", "useLinking", "independent", "prefixes", "useEffect", "current", "set", "getStateFromPath", "getPathFromState", "getActionFromState", "isResolved", "initialState", "useThenable", "useImperativeHandle", "linkingContext", "useMemo", "options", "isReady", "onReadyRef", "NavigationContainer", "forwardRef"], "sourceRoot": "../../src", "sources": ["NavigationContainer.tsx"], "mappings": ";;;;;;AAAA;AAUA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAwC;AAAA;AAAA;AAAA;AASxCA,MAAM,CAACC,yBAAyB,GAAG,IAAIC,OAAO,EAAE;AAUhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwB,OAS/BC,GAA6D,EAC7D;EAAA,IATA;IACEC,KAAK,GAAGC,qBAAY;IACpBC,OAAO;IACPC,QAAQ,GAAG,IAAI;IACfC,aAAa;IACbC,OAAO;IACP,GAAGC;EACiB,CAAC;EAGvB,MAAMC,gBAAgB,GAAGL,OAAO,GAAGA,OAAO,CAACM,OAAO,KAAK,KAAK,GAAG,KAAK;EAEpE,IAAIN,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEO,MAAM,EAAE;IACnB,IAAAC,wBAAkB,EAACR,OAAO,CAACO,MAAM,CAAC;EACpC;EAEA,MAAME,YAAY,GAChBC,KAAK,CAACC,MAAM,CAAwC,IAAI,CAAC;EAE3D,IAAAC,sBAAa,EAACH,YAAY,CAAC;EAC3B,IAAAI,yBAAgB,EAACJ,YAAY,EAAEP,aAAa,CAAC;EAE7C,MAAM;IAAEY;EAAgB,CAAC,GAAG,IAAAC,mBAAU,EAACN,YAAY,EAAE;IACnDO,WAAW,EAAEZ,IAAI,CAACY,WAAW;IAC7BV,OAAO,EAAED,gBAAgB;IACzBY,QAAQ,EAAE,EAAE;IACZ,GAAGjB;EACL,CAAC,CAAC;;EAEF;EACA;EACAU,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,IAAIT,YAAY,CAACU,OAAO,EAAE;MACxBzB,yBAAyB,CAAC0B,GAAG,CAACX,YAAY,CAACU,OAAO,EAAE;QAClD,IAAInB,OAAO,GAAG;UACZ,OAAO;YACL,GAAGA,OAAO;YACVM,OAAO,EAAED,gBAAgB;YACzBY,QAAQ,EAAE,CAAAjB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiB,QAAQ,KAAI,EAAE;YACjCI,gBAAgB,EAAE,CAAArB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqB,gBAAgB,KAAIA,sBAAgB;YAC/DC,gBAAgB,EAAE,CAAAtB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsB,gBAAgB,KAAIA,sBAAgB;YAC/DC,kBAAkB,EAChB,CAAAvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuB,kBAAkB,KAAIA;UACnC,CAAC;QACH;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAG,IAAAC,oBAAW,EAACZ,eAAe,CAAC;EAE/DJ,KAAK,CAACiB,mBAAmB,CAAC9B,GAAG,EAAE,MAAMY,YAAY,CAACU,OAAO,CAAC;EAE1D,MAAMS,cAAc,GAAGlB,KAAK,CAACmB,OAAO,CAAC,OAAO;IAAEC,OAAO,EAAE9B;EAAQ,CAAC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAE7E,MAAM+B,OAAO,GAAG3B,IAAI,CAACqB,YAAY,IAAI,IAAI,IAAI,CAACpB,gBAAgB,IAAImB,UAAU;EAE5E,MAAMQ,UAAU,GAAGtB,KAAK,CAACC,MAAM,CAACR,OAAO,CAAC;EAExCO,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpBc,UAAU,CAACb,OAAO,GAAGhB,OAAO;EAC9B,CAAC,CAAC;EAEFO,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,IAAIa,OAAO,EAAE;MAAA;MACX,uBAAAC,UAAU,CAACb,OAAO,wDAAlB,yBAAAa,UAAU,CAAY;IACxB;EACF,CAAC,EAAE,CAACD,OAAO,CAAC,CAAC;EAEb,IAAI,CAACA,OAAO,EAAE;IACZ;IACA;IACA,OAAO9B,QAAQ;EACjB;EAEA,oBACE,oBAAC,uBAAc,CAAC,QAAQ;IAAC,KAAK,EAAE2B;EAAe,gBAC7C,oBAAC,sBAAa;IAAC,KAAK,EAAE9B;EAAM,gBAC1B,oBAAC,6BAAuB,eAClBM,IAAI;IACR,YAAY,EACVA,IAAI,CAACqB,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGrB,IAAI,CAACqB,YACjD;IACD,GAAG,EAAEhB;EAAa,GAClB,CACY,CACQ;AAE9B;AAEA,MAAMwB,mBAAmB,gBAAGvB,KAAK,CAACwB,UAAU,CAACtC,wBAAwB,CAM9C;AAAC,eAETqC,mBAAmB;AAAA"}
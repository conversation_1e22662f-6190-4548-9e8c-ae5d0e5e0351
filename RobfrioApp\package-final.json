{"name": "RobfrioApp", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest"}, "dependencies": {"react": "18.3.1", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-vector-icons": "^10.0.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/metro-config": "^0.72.11", "metro-react-native-babel-preset": "0.76.8", "typescript": "4.8.4"}}
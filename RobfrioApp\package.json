{"name": "RobfrioApp", "version": "2.0.0", "private": true, "description": "Aplicativo mobile da ROBFRIO - Refrigeração Comercial", "author": "ROBFRIO Team", "license": "MIT", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "clean": "react-native clean", "web-demo": "cd web-demo && python -m http.server 8080", "build-android": "cd android && ./gradlew assembleRelease", "build-ios": "cd ios && xcodebuild -workspace RobfrioApp.xcworkspace -scheme RobfrioApp -configuration Release"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/stack": "^6.3.20", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-vector-icons": "^10.0.2", "react-native-linear-gradient": "^2.8.3", "react-native-animatable": "^1.3.3", "@react-native-async-storage/async-storage": "^1.19.5", "react-native-device-info": "^10.11.0", "react-native-permissions": "^4.1.5", "react-native-share": "^10.0.2", "react-native-image-picker": "^7.1.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/runtime": "^7.23.0", "@react-native/metro-config": "^0.72.11", "metro-react-native-babel-preset": "0.76.8", "typescript": "^5.2.0", "@types/react": "^18.2.0", "@types/react-native": "^0.72.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.50.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.7.0", "@testing-library/react-native": "^12.4.0", "react-test-renderer": "18.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["react-native", "mobile", "refrigeração", "comercial", "rob<PERSON><PERSON>", "ios", "android"]}
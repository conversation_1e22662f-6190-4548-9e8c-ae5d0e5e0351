<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROBFRIO App v2.0 - Demo Premium</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .demo-info {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            color: #1e40af;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .phone-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background: #000;
            border-radius: 0 0 20px 20px;
            z-index: 10;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #1e40af;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .app-header {
            height: 60px;
            background: #1e40af;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .app-content {
            height: calc(100% - 164px);
            overflow-y: auto;
            background: #f8fafc;
        }

        .bottom-tabs {
            height: 60px;
            background: white;
            display: flex;
            border-top: 1px solid #e2e8f0;
        }

        .tab {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px;
            position: relative;
        }

        .tab.active {
            color: #1e40af;
            background: rgba(30, 64, 175, 0.05);
        }

        .tab.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: #1e40af;
            border-radius: 0 0 3px 3px;
        }

        .tab:not(.active) {
            color: #64748b;
        }

        .tab:hover {
            background: rgba(30, 64, 175, 0.1);
            transform: scale(1.05);
        }

        .tab-icon {
            font-size: 20px;
            margin-bottom: 2px;
        }

        .tab-label {
            font-size: 10px;
            font-weight: 500;
        }

        .screen-content {
            display: none;
            padding: 0;
            height: 100%;
        }

        .screen-content.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        .welcome-section {
            background: #e0f2fe;
            padding: 12px;
            text-align: center;
        }

        .welcome-text {
            font-size: 12px;
            color: #0369a1;
            font-weight: 500;
        }

        .weather-section {
            background: white;
            margin: 16px;
            padding: 20px;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .weather-title {
            font-size: 16px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 8px;
        }

        .weather-temp {
            font-size: 28px;
            font-weight: bold;
            color: #059669;
            margin-bottom: 4px;
        }

        .hero-section {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            margin: 16px;
            padding: 30px 20px;
            color: white;
            text-align: center;
            border-radius: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
        }

        .hero-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 12px;
            line-height: 1.3;
        }

        .hero-accent {
            color: #fbbf24;
        }

        .btn-primary {
            background: #f97316;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(249, 115, 22, 0.3);
            margin: 5px;
        }

        .btn-primary:hover {
            background: #ea580c;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(249, 115, 22, 0.4);
        }

        .stats-section {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background: white;
            margin: 0 16px 20px;
            border-radius: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .stat-card {
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 10px;
            color: #64748b;
            font-weight: 500;
        }

        .notification {
            position: fixed;
            top: 100px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
            z-index: 1001;
            animation: slideIn 0.3s ease-out;
            display: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin: 16px;
        }

        .action-button {
            background: white;
            border: none;
            padding: 12px 8px;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        .action-emoji {
            font-size: 18px;
            margin-bottom: 6px;
            display: block;
        }

        .action-text {
            font-size: 10px;
            color: #64748b;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="demo-info">📱 ROBFRIO App v2.0 - Demo Premium Interativo</div>
    
    <div class="notification" id="notification">
        ✅ Funcionalidade ativada com sucesso!
    </div>
    
    <div class="phone-container">
        <div class="phone-screen">
            <div class="status-bar">
                <span>9:41</span>
                <span>ROBFRIO</span>
                <span>🔋 100%</span>
            </div>
            
            <div class="app-header">
                <span id="header-title">ROBFRIO</span>
            </div>
            
            <div class="app-content" id="app-content">
                <!-- Home Screen -->
                <div id="home-screen" class="screen-content active">
                    <div class="welcome-section">
                        <div class="welcome-text">Bem-vindo de volta! Última visita: hoje</div>
                    </div>
                    
                    <div class="weather-section">
                        <div class="weather-title">🌡️ Condições Ideais</div>
                        <div class="weather-temp">25°C</div>
                        <div style="font-size: 14px; color: #059669; margin-bottom: 8px;">Ideal para refrigeração</div>
                        <div style="font-size: 12px; color: #64748b; font-style: italic;">Ótimo momento para manutenção preventiva</div>
                    </div>
                    
                    <div class="hero-section">
                        <div class="hero-title">
                            Soluções Completas em<br>
                            <span class="hero-accent">Refrigeração Comercial</span>
                        </div>
                        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 20px;">
                            Há mais de 13 anos no mercado, oferecemos serviços especializados
                        </div>
                        <button class="btn-primary" onclick="showNotification('WhatsApp')">📱 Solicitar Orçamento</button>
                        <button class="btn-primary" onclick="showNotification('Ligação')">📞 Ligar Agora</button>
                    </div>
                    
                    <div class="stats-section">
                        <div class="stat-card">
                            <div class="stat-number">13+</div>
                            <div class="stat-label">Anos</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">Projetos</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">450+</div>
                            <div class="stat-label">Clientes</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">24h</div>
                            <div class="stat-label">Atendimento</div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="action-button" onclick="showNotification('WhatsApp')">
                            <span class="action-emoji">💬</span>
                            <span class="action-text">WhatsApp</span>
                        </button>
                        <button class="action-button" onclick="showNotification('Ligação')">
                            <span class="action-emoji">📞</span>
                            <span class="action-text">Ligar</span>
                        </button>
                        <button class="action-button" onclick="showNotification('Email')">
                            <span class="action-emoji">📧</span>
                            <span class="action-text">E-mail</span>
                        </button>
                        <button class="action-button" onclick="showNotification('Emergência')">
                            <span class="action-emoji">🚨</span>
                            <span class="action-text">Emergência</span>
                        </button>
                    </div>
                </div>
                
                <!-- About Screen -->
                <div id="about-screen" class="screen-content">
                    <div style="padding: 20px; text-align: center;">
                        <h2 style="color: #1e40af; margin-bottom: 20px;">Sobre a ROBFRIO</h2>
                        <p style="color: #64748b; line-height: 1.6; margin-bottom: 20px;">
                            Desde 2011 no mercado, somos especialistas em refrigeração comercial e câmaras frigoríficas.
                        </p>
                        <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <h3 style="color: #1e40af; margin-bottom: 15px;">Nossos Valores</h3>
                            <div style="text-align: left;">
                                <p style="margin-bottom: 10px;">✅ Qualidade garantida</p>
                                <p style="margin-bottom: 10px;">✅ Atendimento 24h</p>
                                <p style="margin-bottom: 10px;">✅ Equipe especializada</p>
                                <p>✅ Preços competitivos</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Services Screen -->
                <div id="services-screen" class="screen-content">
                    <div style="padding: 20px;">
                        <h2 style="color: #1e40af; text-align: center; margin-bottom: 20px;">Nossos Serviços</h2>
                        
                        <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 15px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <h3 style="color: #1e40af; margin-bottom: 10px;">❄️ Refrigeração Comercial</h3>
                            <p style="color: #64748b; font-size: 14px;">Instalação, manutenção e modernização de sistemas completos</p>
                        </div>
                        
                        <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 15px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <h3 style="color: #1e40af; margin-bottom: 10px;">🏪 Câmaras Frigoríficas</h3>
                            <p style="color: #64748b; font-size: 14px;">Projeto personalizado, montagem e sistemas de controle</p>
                        </div>
                        
                        <div style="background: #fef3c7; padding: 20px; border-radius: 12px; border-left: 4px solid #f59e0b;">
                            <h3 style="color: #92400e; margin-bottom: 10px;">🎉 Promoção Especial</h3>
                            <p style="color: #dc2626; font-weight: bold; font-size: 18px; margin-bottom: 8px;">15% OFF</p>
                            <p style="color: #78350f; font-size: 12px; margin-bottom: 12px;">Manutenção preventiva com desconto especial</p>
                            <button class="btn-primary" onclick="showNotification('Promoção')">Aproveitar Oferta</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bottom-tabs">
                <div class="tab active" onclick="switchTab('home', 'ROBFRIO')">
                    <div class="tab-icon">🏠</div>
                    <div class="tab-label">Início</div>
                </div>
                <div class="tab" onclick="switchTab('about', 'Sobre Nós')">
                    <div class="tab-icon">ℹ️</div>
                    <div class="tab-label">Sobre</div>
                </div>
                <div class="tab" onclick="switchTab('services', 'Serviços')">
                    <div class="tab-icon">🛠️</div>
                    <div class="tab-label">Serviços</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabName, headerTitle) {
            // Remove active class from all tabs and screens
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.screen-content').forEach(screen => screen.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding screen
            event.target.closest('.tab').classList.add('active');
            document.getElementById(tabName + '-screen').classList.add('active');
            
            // Update header title
            document.getElementById('header-title').textContent = headerTitle;
        }

        function showNotification(action) {
            const notification = document.getElementById('notification');
            notification.textContent = `✅ ${action} ativado com sucesso!`;
            notification.style.display = 'block';
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        // Auto-update stats
        setInterval(() => {
            const projectsElement = document.querySelector('.stat-number');
            if (projectsElement && projectsElement.textContent.includes('+')) {
                const currentNumber = parseInt(projectsElement.textContent);
                projectsElement.textContent = (currentNumber + 1) + '+';
            }
        }, 5000);
    </script>
</body>
</html>

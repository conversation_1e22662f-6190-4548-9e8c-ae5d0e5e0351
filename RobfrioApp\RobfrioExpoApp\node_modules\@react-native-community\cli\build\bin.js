#!/usr/bin/env node
"use strict";

require("./tools/gracefulifyFs");
function _semver() {
  const data = _interopRequireDefault(require("semver"));
  _semver = function () {
    return data;
  };
  return data;
}
function _chalk() {
  const data = _interopRequireDefault(require("chalk"));
  _chalk = function () {
    return data;
  };
  return data;
}
function _cliDoctor() {
  const data = require("@react-native-community/cli-doctor");
  _cliDoctor = function () {
    return data;
  };
  return data;
}
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
if (_semver().default.satisfies(process.version, _cliDoctor().versionRanges.NODE_JS)) {
  const {
    run
  } = require('./');
  run();
} else {
  console.error(`${_chalk().default.red(`React Native needs Node.js ${_cliDoctor().versionRanges.NODE_JS}. You're currently on version ${process.version}. Please upgrade Node.js to a supported version and try again.`)}`);
}

//# sourceMappingURL=bin.ts.map
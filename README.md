# ROBFRIO - Site Institucional

Site moderno e responsivo para a empresa ROBFRIO, especializada em refrigeração comercial e câmaras frigoríficas.

## 🚀 Características

- **Design Moderno**: Interface profissional com paleta de cores azul/branco/laranja
- **Totalmente Responsivo**: Otimizado para desktop, tablet e mobile
- **Sistema de Agendamento**: Formulário funcional para solicitação de orçamentos
- **Integração WhatsApp**: Botão flutuante e links diretos
- **Galeria de Projetos**: Showcase com filtros e modal
- **Animações Suaves**: Transições e efeitos visuais elegantes
- **SEO Otimizado**: Meta tags e Schema markup implementados

## 📁 Estrutura do Projeto

```
robfrio-site/
├── index.html              # Página principal
├── assets/
│   ├── css/
│   │   └── style.css       # Estilos principais
│   ├── js/
│   │   └── script.js       # Funcionalidades JavaScript
│   └── images/
│       ├── logo.png        # Logo da empresa
│       ├── hero-bg.jpg     # Imagem do hero
│       └── projetos/       # Imagens dos projetos
└── README.md               # Este arquivo
```

## 🎨 Paleta de Cores

- **Azul Principal**: #1e40af
- **Azul Claro**: #3b82f6
- **Laranja Accent**: #f97316
- **Branco**: #ffffff
- **Cinza**: #64748b
- **Cinza Escuro**: #334155

## 📱 Seções do Site

### 1. Header/Navegação
- Logo da ROBFRIO
- Menu responsivo com hambúrguer para mobile
- Navegação sticky com links suaves

### 2. Hero Section
- Título impactante sobre refrigeração comercial
- Destaque para 13+ anos de experiência
- CTAs para orçamento e informações
- Imagem de fundo profissional

### 3. Sobre a Empresa
- História desde 2011
- Valores e diferenciais
- Estatísticas de projetos realizados
- Cards com ícones representativos

### 4. Serviços
- **Refrigeração Comercial**
  - Instalação de sistemas
  - Manutenção preventiva/corretiva
  - Modernização de equipamentos
- **Câmaras Frigoríficas**
  - Projeto personalizado
  - Montagem completa
  - Isolamento térmico
  - Sistemas de controle

### 5. Galeria de Projetos
- Grid responsivo com projetos realizados
- Filtros por categoria (Todos, Comercial, Câmaras)
- Modal para visualização detalhada
- Animações de hover

### 6. Sistema de Agendamento
- Formulário completo com validação
- Campos obrigatórios e opcionais
- Validação de email e telefone
- Integração com WhatsApp
- Notificações de sucesso/erro

### 7. Contato
- Informações completas da empresa
- Mapa integrado do Google Maps
- Cards com ícones para cada tipo de contato
- Links diretos para telefone e email

### 8. Footer
- Links organizados por categoria
- Redes sociais
- Informações legais
- Design responsivo

## 🛠️ Funcionalidades JavaScript

### Navegação
- Menu mobile responsivo
- Scroll suave entre seções
- Indicador de seção ativa
- Header com efeito de scroll

### Formulários
- Validação em tempo real
- Máscara para telefone brasileiro
- Verificação de email válido
- Validação de datas futuras
- Envio via WhatsApp

### Galeria
- Sistema de filtros dinâmico
- Modal para visualização ampliada
- Animações de entrada
- Navegação por teclado

### Interações
- Botão WhatsApp flutuante com animação
- Notificações toast personalizadas
- Animações de scroll
- Efeitos hover suaves

## 📱 Responsividade

### Breakpoints
- **Desktop**: 1200px+
- **Tablet**: 768px - 1199px
- **Mobile**: 320px - 767px

### Adaptações Mobile
- Menu hambúrguer
- Cards em coluna única
- Botões full-width
- Textos otimizados
- Imagens responsivas

## 🔧 Tecnologias Utilizadas

- **HTML5**: Semântico e acessível
- **CSS3**: Flexbox, Grid, Animações
- **JavaScript**: ES6+, Vanilla JS
- **Font Awesome**: Ícones
- **Google Fonts**: Inter e Poppins
- **Google Maps**: Integração de mapa

## 📞 Configurações de Contato

Para personalizar as informações de contato, edite os seguintes elementos:

### Telefones
```javascript
// No arquivo script.js, linha ~140
const whatsappUrl = `https://wa.me/5519988214177?text=${encodeURIComponent(message)}`;

// No HTML, seção de contato
<a href="tel:+5519988214177">(19) 98821-4177</a>
```

### Email
```html
<!-- No HTML, seção de contato -->
<a href="mailto:<EMAIL>"><EMAIL></a>
```

### Endereço
```html
<!-- No HTML, seção de contato -->
<p class="contact__description">
    Rua das Indústrias, 123<br>
    Distrito Industrial<br>
    São Paulo - SP, 01234-567
</p>
```

## 🖼️ Imagens Necessárias

Para o funcionamento completo do site, adicione as seguintes imagens na pasta `assets/images/`:

- `logo.png` - Logo da empresa (recomendado: 200x200px)
- `hero-bg.jpg` - Imagem do hero (recomendado: 1920x1080px)
- `projetos/projeto1.jpg` - Projeto 1 (recomendado: 600x400px)
- `projetos/projeto2.jpg` - Projeto 2 (recomendado: 600x400px)
- `projetos/projeto3.jpg` - Projeto 3 (recomendado: 600x400px)
- `projetos/projeto4.jpg` - Projeto 4 (recomendado: 600x400px)

## 🚀 Como Usar

1. **Clone ou baixe** os arquivos do projeto
2. **Adicione as imagens** na pasta `assets/images/`
3. **Personalize** as informações de contato
4. **Teste** em diferentes dispositivos
5. **Publique** em seu servidor web

## 📈 SEO e Performance

- Meta tags otimizadas
- Schema markup para negócios locais
- Imagens com lazy loading
- CSS e JS minificados
- Estrutura semântica
- Velocidade de carregamento otimizada

## 🎯 Próximos Passos

- Adicionar mais projetos na galeria
- Implementar blog/notícias
- Integrar com CRM
- Adicionar chat online
- Implementar analytics
- Otimizar ainda mais a performance

---

**ROBFRIO** - Refrigeração Comercial desde 2011
Desenvolvido com ❄️ e ☕

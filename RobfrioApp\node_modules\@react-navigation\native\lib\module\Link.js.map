{"version": 3, "names": ["React", "Platform", "Text", "useLinkProps", "Link", "to", "action", "rest", "props", "onPress", "e", "createElement", "select", "web", "onClick", "default"], "sourceRoot": "../../src", "sources": ["Link.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAgCC,QAAQ,EAAEC,IAAI,QAAmB,cAAc;AAE/E,OAAOC,YAAY,MAAM,gBAAgB;AAezC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,IAAI,OAIP;EAAA,IAJyD;IAC5EC,EAAE;IACFC,MAAM;IACN,GAAGC;EACa,CAAC;EACjB,MAAMC,KAAK,GAAGL,YAAY,CAAY;IAAEE,EAAE;IAAEC;EAAO,CAAC,CAAC;EAErD,MAAMG,OAAO,GACXC,CAA0E,IACvE;IACH,IAAI,SAAS,IAAIH,IAAI,EAAE;MAAA;MACrB,iBAAAA,IAAI,CAACE,OAAO,kDAAZ,mBAAAF,IAAI,EAAWG,CAAC,CAAC;IACnB;IAEAF,KAAK,CAACC,OAAO,CAACC,CAAC,CAAC;EAClB,CAAC;EAED,oBAAOV,KAAK,CAACW,aAAa,CAACT,IAAI,EAAE;IAC/B,GAAGM,KAAK;IACR,GAAGD,IAAI;IACP,GAAGN,QAAQ,CAACW,MAAM,CAAC;MACjBC,GAAG,EAAE;QAAEC,OAAO,EAAEL;MAAQ,CAAQ;MAChCM,OAAO,EAAE;QAAEN;MAAQ;IACrB,CAAC;EACH,CAAC,CAAC;AACJ"}
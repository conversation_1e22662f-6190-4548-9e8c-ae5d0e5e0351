{"version": 3, "names": ["ReservedNameError", "CLIError", "constructor", "name"], "sources": ["../../../../src/commands/init/errors/ReservedNameError.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\n\nexport default class ReservedNameError extends CLIError {\n  constructor(name: string) {\n    super(\n      `Not a valid name for a project. Please do not use the reserved word \"${name}\".`,\n    );\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEe,MAAMA,iBAAiB,SAASC,oBAAQ,CAAC;EACtDC,WAAW,CAACC,IAAY,EAAE;IACxB,KAAK,CACF,wEAAuEA,IAAK,IAAG,CACjF;EACH;AACF;AAAC"}
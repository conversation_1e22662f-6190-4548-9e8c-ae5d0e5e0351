import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Linking,
  Dimensions,
} from 'react-native';
import {RouteProp, useRoute} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {RootStackParamList} from '../../App';

const {width} = Dimensions.get('window');

type ProjectDetailRouteProp = RouteProp<RootStackParamList, 'ProjectDetail'>;

const ProjectDetailScreen: React.FC = () => {
  const route = useRoute<ProjectDetailRouteProp>();
  const {project} = route.params;

  const handleWhatsApp = () => {
    const message = `Olá! Gostaria de saber mais sobre o projeto "${project.title}" e solicitar um orçamento similar.`;
    const url = `whatsapp://send?phone=5511999999999&text=${encodeURIComponent(message)}`;
    Linking.openURL(url).catch(() => {
      Linking.openURL(`https://wa.me/5511999999999?text=${encodeURIComponent(message)}`);
    });
  };

  const handleCall = () => {
    Linking.openURL('tel:+5511999999999');
  };

  const handleEmail = () => {
    const subject = `Interesse no projeto: ${project.title}`;
    const body = `Olá,\n\nGostaria de saber mais sobre o projeto "${project.title}" e solicitar um orçamento similar.\n\nAguardo retorno.`;
    Linking.openURL(`mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Project Image */}
      <View style={styles.imageContainer}>
        {project.image ? (
          <Image source={project.image} style={styles.projectImage} />
        ) : (
          <View style={[styles.projectImage, {backgroundColor: '#e2e8f0', justifyContent: 'center', alignItems: 'center'}]}>
            <Icon name="image" size={60} color="#94a3b8" />
          </View>
        )}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)']}
          style={styles.imageOverlay}>
          <View style={styles.imageInfo}>
            <Text style={styles.projectTitle}>{project.title}</Text>
            <Text style={styles.projectSubtitle}>{project.description}</Text>
          </View>
        </LinearGradient>
      </View>

      {/* Project Info */}
      <View style={styles.infoSection}>
        <View style={styles.infoGrid}>
          <View style={styles.infoCard}>
            <LinearGradient
              colors={['#1e40af', '#3b82f6']}
              style={styles.infoIcon}>
              <Icon name="thermostat" size={24} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.infoLabel}>Temperatura</Text>
            <Text style={styles.infoValue}>{project.temperature}</Text>
          </View>
          
          <View style={styles.infoCard}>
            <LinearGradient
              colors={['#f97316', '#fb923c']}
              style={styles.infoIcon}>
              <Icon name="location-on" size={24} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.infoLabel}>Localização</Text>
            <Text style={styles.infoValue}>{project.location}</Text>
          </View>
          
          <View style={styles.infoCard}>
            <LinearGradient
              colors={['#10b981', '#34d399']}
              style={styles.infoIcon}>
              <Icon name="event" size={24} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.infoLabel}>Ano</Text>
            <Text style={styles.infoValue}>{project.year}</Text>
          </View>
          
          <View style={styles.infoCard}>
            <LinearGradient
              colors={['#8b5cf6', '#a78bfa']}
              style={styles.infoIcon}>
              <Icon name="category" size={24} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.infoLabel}>Categoria</Text>
            <Text style={styles.infoValue}>
              {project.category === 'comercial' ? 'Comercial' : 'Câmaras'}
            </Text>
          </View>
        </View>
      </View>

      {/* Project Description */}
      <View style={styles.descriptionSection}>
        <Text style={styles.sectionTitle}>Sobre o Projeto</Text>
        <Text style={styles.descriptionText}>{project.details}</Text>
      </View>

      {/* Features */}
      <View style={styles.featuresSection}>
        <Text style={styles.sectionTitle}>Características Técnicas</Text>
        <View style={styles.featuresList}>
          <View style={styles.featureItem}>
            <Icon name="check-circle" size={20} color="#10b981" />
            <Text style={styles.featureText}>Sistema de controle automático</Text>
          </View>
          <View style={styles.featureItem}>
            <Icon name="check-circle" size={20} color="#10b981" />
            <Text style={styles.featureText}>Monitoramento 24h</Text>
          </View>
          <View style={styles.featureItem}>
            <Icon name="check-circle" size={20} color="#10b981" />
            <Text style={styles.featureText}>Eficiência energética</Text>
          </View>
          <View style={styles.featureItem}>
            <Icon name="check-circle" size={20} color="#10b981" />
            <Text style={styles.featureText}>Backup de emergência</Text>
          </View>
          <View style={styles.featureItem}>
            <Icon name="check-circle" size={20} color="#10b981" />
            <Text style={styles.featureText}>Garantia estendida</Text>
          </View>
        </View>
      </View>

      {/* Benefits */}
      <View style={styles.benefitsSection}>
        <Text style={styles.sectionTitle}>Benefícios Alcançados</Text>
        <View style={styles.benefitsList}>
          <View style={styles.benefitCard}>
            <LinearGradient
              colors={['#1e40af', '#3b82f6']}
              style={styles.benefitIcon}>
              <Icon name="trending-down" size={24} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.benefitTitle}>Redução de Custos</Text>
            <Text style={styles.benefitDescription}>
              Economia de até 30% no consumo de energia
            </Text>
          </View>
          
          <View style={styles.benefitCard}>
            <LinearGradient
              colors={['#f97316', '#fb923c']}
              style={styles.benefitIcon}>
              <Icon name="speed" size={24} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.benefitTitle}>Maior Eficiência</Text>
            <Text style={styles.benefitDescription}>
              Controle preciso de temperatura e umidade
            </Text>
          </View>
          
          <View style={styles.benefitCard}>
            <LinearGradient
              colors={['#10b981', '#34d399']}
              style={styles.benefitIcon}>
              <Icon name="security" size={24} color="#ffffff" />
            </LinearGradient>
            <Text style={styles.benefitTitle}>Maior Segurança</Text>
            <Text style={styles.benefitDescription}>
              Sistema de backup e alertas automáticos
            </Text>
          </View>
        </View>
      </View>

      {/* Contact CTA */}
      <View style={styles.ctaSection}>
        <Text style={styles.ctaTitle}>Interessado em um projeto similar?</Text>
        <Text style={styles.ctaDescription}>
          Entre em contato conosco e receba um orçamento personalizado
        </Text>
        
        <View style={styles.ctaButtons}>
          <TouchableOpacity style={styles.whatsappButton} onPress={handleWhatsApp}>
            <Icon name="chat" size={20} color="#ffffff" />
            <Text style={styles.whatsappButtonText}>WhatsApp</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.callButton} onPress={handleCall}>
            <Icon name="phone" size={20} color="#ffffff" />
            <Text style={styles.callButtonText}>Ligar</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.emailButton} onPress={handleEmail}>
            <Icon name="email" size={20} color="#ffffff" />
            <Text style={styles.emailButtonText}>E-mail</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  imageContainer: {
    position: 'relative',
    height: 300,
  },
  projectImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
    justifyContent: 'flex-end',
    padding: 20,
  },
  imageInfo: {
    marginBottom: 20,
  },
  projectTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  projectSubtitle: {
    fontSize: 16,
    color: '#e2e8f0',
    lineHeight: 24,
  },
  infoSection: {
    padding: 20,
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  infoCard: {
    width: (width - 56) / 2,
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 12,
    color: '#64748b',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1e40af',
    textAlign: 'center',
  },
  descriptionSection: {
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 16,
  },
  descriptionText: {
    fontSize: 16,
    color: '#64748b',
    lineHeight: 24,
    textAlign: 'justify',
  },
  featuresSection: {
    padding: 20,
  },
  featuresList: {
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureText: {
    fontSize: 16,
    color: '#64748b',
    flex: 1,
  },
  benefitsSection: {
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  benefitsList: {
    gap: 16,
  },
  benefitCard: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  benefitIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 4,
    flex: 1,
  },
  benefitDescription: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
    flex: 1,
  },
  ctaSection: {
    padding: 20,
    alignItems: 'center',
  },
  ctaTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 8,
    textAlign: 'center',
  },
  ctaDescription: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  ctaButtons: {
    flexDirection: 'row',
    gap: 12,
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  whatsappButton: {
    backgroundColor: '#25d366',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  whatsappButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  callButton: {
    backgroundColor: '#1e40af',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  callButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  emailButton: {
    backgroundColor: '#f97316',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  emailButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProjectDetailScreen;

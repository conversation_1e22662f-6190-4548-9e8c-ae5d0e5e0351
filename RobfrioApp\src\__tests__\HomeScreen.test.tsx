/**
 * Testes para HomeScreen
 * Testa funcionalidades principais da tela inicial
 */

import React from 'react';
import {render, fireEvent, waitFor} from '@testing-library/react-native';
import {Alert, Linking} from 'react-native';
import HomeScreen from '../screens/HomeScreen';

// Mock dos serviços
jest.mock('../services/AnalyticsService');
jest.mock('../services/NotificationService');

// Mock do react-navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
  useFocusEffect: jest.fn(),
}));

describe('HomeScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(Alert, 'alert').mockImplementation(() => {});
    jest.spyOn(Linking, 'openURL').mockResolvedValue(true);
    jest.spyOn(Linking, 'canOpenURL').mockResolvedValue(true);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('deve renderizar corretamente', () => {
    const {getByText} = render(<HomeScreen />);
    
    expect(getByText('Soluções Completas em')).toBeTruthy();
    expect(getByText('Refrigeração Comercial')).toBeTruthy();
    expect(getByText('📱 Solicitar Orçamento')).toBeTruthy();
    expect(getByText('📞 Ligar Agora')).toBeTruthy();
  });

  it('deve exibir estatísticas da empresa', () => {
    const {getByText} = render(<HomeScreen />);
    
    expect(getByText('Anos de Experiência')).toBeTruthy();
    expect(getByText('Projetos Realizados')).toBeTruthy();
    expect(getByText('Clientes Satisfeitos')).toBeTruthy();
    expect(getByText('Atendimento')).toBeTruthy();
  });

  it('deve exibir seção de serviços', () => {
    const {getByText} = render(<HomeScreen />);
    
    expect(getByText('Nossos Serviços')).toBeTruthy();
    expect(getByText('Refrigeração Comercial')).toBeTruthy();
    expect(getByText('Câmaras Frigoríficas')).toBeTruthy();
  });

  it('deve exibir ações rápidas', () => {
    const {getByText} = render(<HomeScreen />);
    
    expect(getByText('Ações Rápidas')).toBeTruthy();
    expect(getByText('WhatsApp')).toBeTruthy();
    expect(getByText('Ligar')).toBeTruthy();
    expect(getByText('E-mail')).toBeTruthy();
    expect(getByText('Emergência')).toBeTruthy();
  });

  it('deve abrir WhatsApp ao pressionar botão de orçamento', async () => {
    const {getByText} = render(<HomeScreen />);
    
    const whatsappButton = getByText('📱 Solicitar Orçamento');
    fireEvent.press(whatsappButton);

    await waitFor(() => {
      expect(Linking.canOpenURL).toHaveBeenCalled();
    });
  });

  it('deve fazer ligação ao pressionar botão de ligar', async () => {
    const {getByText} = render(<HomeScreen />);
    
    const callButton = getByText('📞 Ligar Agora');
    fireEvent.press(callButton);

    await waitFor(() => {
      expect(Linking.openURL).toHaveBeenCalledWith('tel:+5511999999999');
    });
  });

  it('deve abrir email ao pressionar botão de email', async () => {
    const {getByText} = render(<HomeScreen />);
    
    const emailButton = getByText('E-mail');
    fireEvent.press(emailButton);

    await waitFor(() => {
      expect(Linking.openURL).toHaveBeenCalledWith(
        expect.stringContaining('mailto:<EMAIL>')
      );
    });
  });

  it('deve mostrar alerta de emergência', async () => {
    const {getByText} = render(<HomeScreen />);
    
    const emergencyButton = getByText('Emergência');
    fireEvent.press(emergencyButton);

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        '🚨 Atendimento de Emergência',
        expect.any(String),
        expect.any(Array)
      );
    });
  });

  it('deve exibir seção de promoções', () => {
    const {getByText} = render(<HomeScreen />);
    
    expect(getByText('🎉 Ofertas Especiais')).toBeTruthy();
    expect(getByText('Manutenção Preventiva')).toBeTruthy();
    expect(getByText('15% OFF')).toBeTruthy();
    expect(getByText('Aproveitar Oferta')).toBeTruthy();
  });

  it('deve exibir informações da empresa', () => {
    const {getByText} = render(<HomeScreen />);
    
    expect(getByText('ROBFRIO')).toBeTruthy();
    expect(getByText(/Especialista em refrigeração comercial/)).toBeTruthy();
    expect(getByText(/App versão 2.0.0/)).toBeTruthy();
  });

  it('deve suportar pull-to-refresh', async () => {
    const {getByTestId} = render(<HomeScreen />);
    
    // Simular pull to refresh
    const scrollView = getByTestId('home-scroll-view');
    fireEvent(scrollView, 'refresh');

    // Verificar se o refresh foi acionado
    await waitFor(() => {
      // O estado de refreshing deve ter sido ativado
      expect(scrollView).toBeTruthy();
    });
  });

  it('deve lidar com erro ao abrir WhatsApp', async () => {
    jest.spyOn(Linking, 'canOpenURL').mockResolvedValue(false);
    
    const {getByText} = render(<HomeScreen />);
    
    const whatsappButton = getByText('📱 Solicitar Orçamento');
    fireEvent.press(whatsappButton);

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'WhatsApp não encontrado',
        expect.any(String),
        expect.any(Array)
      );
    });
  });

  it('deve rastrear eventos de analytics', async () => {
    const {getByText} = render(<HomeScreen />);
    
    const whatsappButton = getByText('📱 Solicitar Orçamento');
    fireEvent.press(whatsappButton);

    // Verificar se o analytics foi chamado
    // (O mock já está configurado nos testes)
    expect(whatsappButton).toBeTruthy();
  });

  it('deve exibir informações do clima', () => {
    const {getByText} = render(<HomeScreen />);
    
    expect(getByText('🌡️ Condições Ideais')).toBeTruthy();
    expect(getByText('25°C')).toBeTruthy();
    expect(getByText('Ideal para refrigeração')).toBeTruthy();
  });

  it('deve permitir teste de notificação', async () => {
    const {getByText} = render(<HomeScreen />);
    
    const notificationButton = getByText('Teste Notif.');
    fireEvent.press(notificationButton);

    // Verificar se o botão foi pressionado
    expect(notificationButton).toBeTruthy();
  });
});

/**
 * Serviço de Analytics
 * Monitora uso do app, eventos e performance
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import DeviceInfo from 'react-native-device-info';

export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp: number;
  sessionId: string;
  userId?: string;
}

export interface UserSession {
  id: string;
  startTime: number;
  endTime?: number;
  screenViews: string[];
  events: AnalyticsEvent[];
  deviceInfo: any;
}

class AnalyticsService {
  private static instance: AnalyticsService;
  private currentSession: UserSession | null = null;
  private isInitialized = false;

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.startNewSession();
      this.isInitialized = true;
      console.log('AnalyticsService inicializado');
    } catch (error) {
      console.error('Erro ao inicializar AnalyticsService:', error);
    }
  }

  private async startNewSession(): Promise<void> {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const deviceInfo = {
      brand: DeviceInfo.getBrand(),
      model: DeviceInfo.getModel(),
      systemName: DeviceInfo.getSystemName(),
      systemVersion: DeviceInfo.getSystemVersion(),
      appVersion: DeviceInfo.getVersion(),
      buildNumber: DeviceInfo.getBuildNumber(),
      uniqueId: await DeviceInfo.getUniqueId(),
      isTablet: DeviceInfo.isTablet(),
    };

    this.currentSession = {
      id: sessionId,
      startTime: Date.now(),
      screenViews: [],
      events: [],
      deviceInfo,
    };

    await this.trackEvent('session_start', {
      sessionId,
      deviceInfo,
    });
  }

  async endSession(): Promise<void> {
    if (!this.currentSession) return;

    this.currentSession.endTime = Date.now();
    const sessionDuration = this.currentSession.endTime - this.currentSession.startTime;

    await this.trackEvent('session_end', {
      sessionId: this.currentSession.id,
      duration: sessionDuration,
      screenViewsCount: this.currentSession.screenViews.length,
      eventsCount: this.currentSession.events.length,
    });

    // Salvar sessão no storage
    await this.saveSession(this.currentSession);
    this.currentSession = null;
  }

  async trackEvent(eventName: string, properties?: Record<string, any>): Promise<void> {
    if (!this.currentSession) {
      await this.startNewSession();
    }

    const event: AnalyticsEvent = {
      name: eventName,
      properties: properties || {},
      timestamp: Date.now(),
      sessionId: this.currentSession!.id,
    };

    this.currentSession!.events.push(event);

    // Log para desenvolvimento
    console.log(`📊 Analytics Event: ${eventName}`, properties);

    // Em produção, enviar para serviço de analytics (Firebase, Mixpanel, etc.)
    await this.sendEventToService(event);
  }

  async trackScreenView(screenName: string): Promise<void> {
    if (!this.currentSession) {
      await this.startNewSession();
    }

    this.currentSession!.screenViews.push(screenName);

    await this.trackEvent('screen_view', {
      screenName,
      timestamp: Date.now(),
    });
  }

  async trackUserAction(action: string, details?: Record<string, any>): Promise<void> {
    await this.trackEvent('user_action', {
      action,
      ...details,
    });
  }

  async trackWhatsAppContact(context: string): Promise<void> {
    await this.trackEvent('whatsapp_contact', {
      context,
      timestamp: Date.now(),
    });
  }

  async trackPhoneCall(context: string): Promise<void> {
    await this.trackEvent('phone_call', {
      context,
      timestamp: Date.now(),
    });
  }

  async trackFormSubmission(formType: string, formData: Record<string, any>): Promise<void> {
    await this.trackEvent('form_submission', {
      formType,
      fieldsCount: Object.keys(formData).length,
      timestamp: Date.now(),
    });
  }

  async trackError(error: Error, context?: string): Promise<void> {
    await this.trackEvent('app_error', {
      errorMessage: error.message,
      errorStack: error.stack,
      context,
      timestamp: Date.now(),
    });
  }

  async trackPerformance(metric: string, value: number, unit: string): Promise<void> {
    await this.trackEvent('performance_metric', {
      metric,
      value,
      unit,
      timestamp: Date.now(),
    });
  }

  private async sendEventToService(event: AnalyticsEvent): Promise<void> {
    try {
      // Em um app real, enviar para Firebase Analytics, Mixpanel, etc.
      // Por enquanto, apenas salvar localmente
      const events = await this.getStoredEvents();
      events.push(event);
      await AsyncStorage.setItem('analytics_events', JSON.stringify(events));
    } catch (error) {
      console.error('Erro ao enviar evento para serviço:', error);
    }
  }

  private async saveSession(session: UserSession): Promise<void> {
    try {
      const sessions = await this.getStoredSessions();
      sessions.push(session);
      
      // Manter apenas as últimas 50 sessões
      if (sessions.length > 50) {
        sessions.splice(0, sessions.length - 50);
      }
      
      await AsyncStorage.setItem('analytics_sessions', JSON.stringify(sessions));
    } catch (error) {
      console.error('Erro ao salvar sessão:', error);
    }
  }

  async getStoredEvents(): Promise<AnalyticsEvent[]> {
    try {
      const stored = await AsyncStorage.getItem('analytics_events');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Erro ao obter eventos armazenados:', error);
      return [];
    }
  }

  async getStoredSessions(): Promise<UserSession[]> {
    try {
      const stored = await AsyncStorage.getItem('analytics_sessions');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Erro ao obter sessões armazenadas:', error);
      return [];
    }
  }

  async getAnalyticsReport(): Promise<{
    totalSessions: number;
    totalEvents: number;
    mostViewedScreens: Array<{screen: string; count: number}>;
    mostTriggeredEvents: Array<{event: string; count: number}>;
    averageSessionDuration: number;
  }> {
    try {
      const sessions = await this.getStoredSessions();
      const events = await this.getStoredEvents();

      // Calcular estatísticas
      const screenViews: Record<string, number> = {};
      const eventCounts: Record<string, number> = {};
      let totalDuration = 0;
      let completedSessions = 0;

      sessions.forEach(session => {
        session.screenViews.forEach(screen => {
          screenViews[screen] = (screenViews[screen] || 0) + 1;
        });

        if (session.endTime) {
          totalDuration += session.endTime - session.startTime;
          completedSessions++;
        }
      });

      events.forEach(event => {
        eventCounts[event.name] = (eventCounts[event.name] || 0) + 1;
      });

      const mostViewedScreens = Object.entries(screenViews)
        .map(([screen, count]) => ({screen, count}))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      const mostTriggeredEvents = Object.entries(eventCounts)
        .map(([event, count]) => ({event, count}))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return {
        totalSessions: sessions.length,
        totalEvents: events.length,
        mostViewedScreens,
        mostTriggeredEvents,
        averageSessionDuration: completedSessions > 0 ? totalDuration / completedSessions : 0,
      };
    } catch (error) {
      console.error('Erro ao gerar relatório de analytics:', error);
      return {
        totalSessions: 0,
        totalEvents: 0,
        mostViewedScreens: [],
        mostTriggeredEvents: [],
        averageSessionDuration: 0,
      };
    }
  }

  async clearAnalyticsData(): Promise<void> {
    try {
      await AsyncStorage.removeItem('analytics_events');
      await AsyncStorage.removeItem('analytics_sessions');
      console.log('Dados de analytics limpos');
    } catch (error) {
      console.error('Erro ao limpar dados de analytics:', error);
    }
  }
}

export default AnalyticsService;

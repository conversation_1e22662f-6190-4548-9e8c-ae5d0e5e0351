/*===== MENU SHOW/HIDE =====*/
const navMenu = document.getElementById('nav-menu'),
      navToggle = document.getElementById('nav-toggle'),
      navClose = document.getElementById('nav-close');

// Menu show
if(navToggle) {
    navToggle.addEventListener('click', () => {
        navMenu.classList.add('show-menu');
    });
}

// Menu hidden
if(navClose) {
    navClose.addEventListener('click', () => {
        navMenu.classList.remove('show-menu');
    });
}

/*===== REMOVE MENU MOBILE =====*/
const navLink = document.querySelectorAll('.nav__link');

function linkAction() {
    const navMenu = document.getElementById('nav-menu');
    navMenu.classList.remove('show-menu');
}
navLink.forEach(n => n.addEventListener('click', linkAction));

/*===== CHANGE BACKGROUND HEADER =====*/
function scrollHeader() {
    const header = document.getElementById('header');
    if(this.scrollY >= 50) header.classList.add('scroll-header');
    else header.classList.remove('scroll-header');
}
window.addEventListener('scroll', scrollHeader);

/*===== SHOW SCROLL UP =====*/
function scrollUp() {
    const scrollUp = document.getElementById('scroll-up');
    if(this.scrollY >= 200) scrollUp.classList.add('show-scroll');
    else scrollUp.classList.remove('show-scroll');
}
window.addEventListener('scroll', scrollUp);

/*===== SCROLL SECTIONS ACTIVE LINK =====*/
const sections = document.querySelectorAll('section[id]');

function scrollActive() {
    const scrollY = window.pageYOffset;

    sections.forEach(current => {
        const sectionHeight = current.offsetHeight;
        const sectionTop = current.offsetTop - 50;
        const sectionId = current.getAttribute('id');

        if(scrollY > sectionTop && scrollY <= sectionTop + sectionHeight) {
            document.querySelector('.nav__menu a[href*=' + sectionId + ']').classList.add('active-link');
        } else {
            document.querySelector('.nav__menu a[href*=' + sectionId + ']').classList.remove('active-link');
        }
    });
}
window.addEventListener('scroll', scrollActive);

/*===== SMOOTH SCROLLING =====*/
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

/*===== PROJECT FILTERS =====*/
const filterButtons = document.querySelectorAll('.filter__btn');
const projectCards = document.querySelectorAll('.project__card');

filterButtons.forEach(button => {
    button.addEventListener('click', () => {
        // Remove active class from all buttons
        filterButtons.forEach(btn => btn.classList.remove('active'));
        // Add active class to clicked button
        button.classList.add('active');
        
        const filterValue = button.getAttribute('data-filter');
        
        projectCards.forEach(card => {
            if (filterValue === 'all' || card.getAttribute('data-category') === filterValue) {
                card.style.display = 'block';
                card.style.animation = 'fadeInUp 0.5s ease';
            } else {
                card.style.display = 'none';
            }
        });
    });
});

/*===== MODAL FUNCTIONALITY =====*/
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// Close modal when clicking outside
window.addEventListener('click', (e) => {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
});

// Close modal with Escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (modal.style.display === 'block') {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    }
});

/*===== FORM VALIDATION AND SUBMISSION =====*/
const scheduleForm = document.getElementById('scheduleForm');

if (scheduleForm) {
    scheduleForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(this);
        const agendamento = {
            nome: formData.get('nome'),
            empresa: formData.get('empresa'),
            telefone: formData.get('telefone'),
            email: formData.get('email'),
            tipoServico: formData.get('tipoServico'),
            dataPreferencial: formData.get('dataPreferencial'),
            descricao: formData.get('descricao'),
            timestamp: new Date().toISOString()
        };
        
        // Validate required fields
        if (!agendamento.nome || !agendamento.telefone || !agendamento.email || !agendamento.tipoServico) {
            showNotification('Por favor, preencha todos os campos obrigatórios.', 'error');
            return;
        }
        
        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(agendamento.email)) {
            showNotification('Por favor, insira um e-mail válido.', 'error');
            return;
        }
        
        // Validate phone format (Brazilian)
        const phoneRegex = /^[\(\)\s\-\+\d]{10,}$/;
        if (!phoneRegex.test(agendamento.telefone)) {
            showNotification('Por favor, insira um telefone válido.', 'error');
            return;
        }
        
        // Validate future date
        if (agendamento.dataPreferencial) {
            const selectedDate = new Date(agendamento.dataPreferencial);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
                showNotification('Por favor, selecione uma data futura.', 'error');
                return;
            }
        }
        
        // Simulate form submission
        submitForm(agendamento);
    });
}

function submitForm(data) {
    // Show loading state
    const submitBtn = document.querySelector('.form__submit');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        // Show success message
        showNotification('Solicitação enviada com sucesso! Entraremos em contato em breve.', 'success');
        
        // Reset form
        document.getElementById('scheduleForm').reset();
        
        // Send to WhatsApp
        sendToWhatsApp(data);
        
    }, 2000);
}

function sendToWhatsApp(data) {
    const message = `
*Nova Solicitação de Orçamento - ROBFRIO*

*Nome:* ${data.nome}
*Empresa:* ${data.empresa || 'Não informado'}
*Telefone:* ${data.telefone}
*E-mail:* ${data.email}
*Serviço:* ${data.tipoServico}
*Data Preferencial:* ${data.dataPreferencial || 'Não informado'}
*Descrição:* ${data.descricao || 'Não informado'}

_Enviado em: ${new Date().toLocaleString('pt-BR')}_
    `.trim();
    
    const whatsappUrl = `https://wa.me/5519988214177?text=${encodeURIComponent(message)}`;
    
    // Ask user if they want to send via WhatsApp
    if (confirm('Deseja enviar esta solicitação também via WhatsApp?')) {
        window.open(whatsappUrl, '_blank');
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification--${type}`;
    notification.innerHTML = `
        <div class="notification__content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification__close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add styles if not already added
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 2rem;
                right: 2rem;
                background: white;
                padding: 1rem 1.5rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                z-index: 1000;
                display: flex;
                align-items: center;
                gap: 1rem;
                max-width: 400px;
                animation: slideInRight 0.3s ease;
            }
            .notification--success { border-left: 4px solid #10b981; }
            .notification--error { border-left: 4px solid #ef4444; }
            .notification--info { border-left: 4px solid #3b82f6; }
            .notification__content { display: flex; align-items: center; gap: 0.5rem; flex: 1; }
            .notification__content i { color: inherit; }
            .notification--success .notification__content i { color: #10b981; }
            .notification--error .notification__content i { color: #ef4444; }
            .notification--info .notification__content i { color: #3b82f6; }
            .notification__close { background: none; border: none; cursor: pointer; color: #64748b; }
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/*===== SCROLL ANIMATIONS =====*/
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', () => {
    const animateElements = document.querySelectorAll('.service__card, .project__card, .about__stat, .contact__card');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        observer.observe(el);
    });
});

/*===== PHONE MASK =====*/
const phoneInput = document.getElementById('telefone');
if (phoneInput) {
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        
        if (value.length <= 11) {
            if (value.length <= 10) {
                value = value.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
            } else {
                value = value.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
            }
        }
        
        e.target.value = value;
    });
}

/*===== LOADING ANIMATION =====*/
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
});

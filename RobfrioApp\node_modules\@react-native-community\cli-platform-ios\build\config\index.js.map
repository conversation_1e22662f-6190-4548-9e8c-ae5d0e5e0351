{"version": 3, "names": ["projectConfig", "folder", "userConfig", "src", "path", "join", "sourceDir", "podfile", "findPodfilePath", "dirname", "xcodeProject", "findXcodeProject", "fs", "readdirSync", "dependencyConfig", "podspecPath", "findPodspec", "configurations", "scriptPhases", "findPodfilePaths", "findAllPodfilePaths"], "sources": ["../../src/config/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport path from 'path';\nimport fs from 'fs';\nimport findPodfilePath from './findPodfilePath';\nimport findXcodeProject from './findXcodeProject';\nimport findPodspec from './findPodspec';\nimport findAllPodfilePaths from './findAllPodfilePaths';\nimport {\n  IOSProjectParams,\n  IOSDependencyParams,\n  IOSProjectConfig,\n  IOSDependencyConfig,\n} from '@react-native-community/cli-types';\n\n/**\n * Returns project config by analyzing given folder and applying some user defaults\n * when constructing final object\n */\nexport function projectConfig(\n  folder: string,\n  userConfig: IOSProjectParams,\n): IOSProjectConfig | null {\n  if (!userConfig) {\n    return null;\n  }\n\n  const src = path.join(folder, userConfig.sourceDir ?? '');\n  const podfile = findPodfilePath(src);\n\n  /**\n   * In certain repos, the Xcode project can be generated by a tool.\n   * The only file that we can assume to exist on disk is `Podfile`.\n   */\n  if (!podfile) {\n    return null;\n  }\n\n  const sourceDir = path.dirname(podfile);\n\n  const xcodeProject = findXcodeProject(fs.readdirSync(sourceDir));\n\n  return {\n    sourceDir,\n    xcodeProject,\n  };\n}\n\nexport function dependencyConfig(\n  folder: string,\n  userConfig: IOSDependencyParams | null = {},\n): IOSDependencyConfig | null {\n  if (userConfig === null) {\n    return null;\n  }\n\n  const podspecPath = findPodspec(folder);\n\n  if (!podspecPath) {\n    return null;\n  }\n\n  return {\n    podspecPath,\n    configurations: userConfig.configurations || [],\n    scriptPhases: userConfig.scriptPhases || [],\n  };\n}\n\nexport const findPodfilePaths = findAllPodfilePaths;\n"], "mappings": ";;;;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;AAAwD;AAZxD;AACA;AACA;AACA;AACA;AACA;AACA;;AAcA;AACA;AACA;AACA;AACO,SAASA,aAAa,CAC3BC,MAAc,EACdC,UAA4B,EACH;EACzB,IAAI,CAACA,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EAEA,MAAMC,GAAG,GAAGC,eAAI,CAACC,IAAI,CAACJ,MAAM,EAAEC,UAAU,CAACI,SAAS,IAAI,EAAE,CAAC;EACzD,MAAMC,OAAO,GAAG,IAAAC,wBAAe,EAACL,GAAG,CAAC;;EAEpC;AACF;AACA;AACA;EACE,IAAI,CAACI,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EAEA,MAAMD,SAAS,GAAGF,eAAI,CAACK,OAAO,CAACF,OAAO,CAAC;EAEvC,MAAMG,YAAY,GAAG,IAAAC,yBAAgB,EAACC,aAAE,CAACC,WAAW,CAACP,SAAS,CAAC,CAAC;EAEhE,OAAO;IACLA,SAAS;IACTI;EACF,CAAC;AACH;AAEO,SAASI,gBAAgB,CAC9Bb,MAAc,EACdC,UAAsC,GAAG,CAAC,CAAC,EACf;EAC5B,IAAIA,UAAU,KAAK,IAAI,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,MAAMa,WAAW,GAAG,IAAAC,oBAAW,EAACf,MAAM,CAAC;EAEvC,IAAI,CAACc,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,OAAO;IACLA,WAAW;IACXE,cAAc,EAAEf,UAAU,CAACe,cAAc,IAAI,EAAE;IAC/CC,YAAY,EAAEhB,UAAU,CAACgB,YAAY,IAAI;EAC3C,CAAC;AACH;AAEO,MAAMC,gBAAgB,GAAGC,4BAAmB;AAAC"}
import * as React from 'react';
import { Animated, StyleProp, ViewProps, ViewStyle } from 'react-native';
import type { EdgeInsets } from 'react-native-safe-area-context';
import type { GestureDirection, Layout, StackCardStyleInterpolator, TransitionSpec } from '../../types';
type Props = ViewProps & {
    interpolationIndex: number;
    closing: boolean;
    next?: Animated.AnimatedInterpolation<number>;
    current: Animated.AnimatedInterpolation<number>;
    gesture: Animated.Value;
    layout: Layout;
    insets: EdgeInsets;
    headerDarkContent: boolean | undefined;
    pageOverflowEnabled: boolean;
    gestureDirection: GestureDirection;
    onOpen: () => void;
    onClose: () => void;
    onTransition: (props: {
        closing: boolean;
        gesture: boolean;
    }) => void;
    onGestureBegin: () => void;
    onGestureCanceled: () => void;
    onGestureEnd: () => void;
    children: React.ReactNode;
    overlay: (props: {
        style: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;
    }) => React.ReactNode;
    overlayEnabled: boolean;
    shadowEnabled: boolean;
    gestureEnabled: boolean;
    gestureResponseDistance?: number;
    gestureVelocityImpact: number;
    transitionSpec: {
        open: TransitionSpec;
        close: TransitionSpec;
    };
    styleInterpolator: StackCardStyleInterpolator;
    containerStyle?: StyleProp<ViewStyle>;
    contentStyle?: StyleProp<ViewStyle>;
};
export default class Card extends React.Component<Props> {
    static defaultProps: {
        shadowEnabled: boolean;
        gestureEnabled: boolean;
        gestureVelocityImpact: number;
        overlay: ({ style, }: {
            style: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;
        }) => JSX.Element | null;
    };
    componentDidMount(): void;
    componentDidUpdate(prevProps: Props): void;
    componentWillUnmount(): void;
    private isCurrentlyMounted;
    private isClosing;
    private inverted;
    private layout;
    private isSwiping;
    private interactionHandle;
    private pendingGestureCallback;
    private lastToValue;
    private animate;
    private getAnimateToValue;
    private setPointerEventsEnabled;
    private handleStartInteraction;
    private handleEndInteraction;
    private handleGestureStateChange;
    private getInterpolatedStyle;
    private getCardAnimation;
    private gestureActivationCriteria;
    private ref;
    render(): JSX.Element;
}
export declare const getIsModalPresentation: (cardStyleInterpolator: StackCardStyleInterpolator) => boolean;
export {};
//# sourceMappingURL=Card.d.ts.map
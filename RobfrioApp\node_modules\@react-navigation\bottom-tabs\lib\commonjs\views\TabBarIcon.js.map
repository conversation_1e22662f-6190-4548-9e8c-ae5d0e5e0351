{"version": 3, "names": ["TabBarIcon", "route", "_", "horizontal", "badge", "badgeStyle", "activeOpacity", "inactiveOpacity", "activeTintColor", "inactiveTintColor", "renderIcon", "style", "size", "styles", "iconHorizontal", "iconVertical", "icon", "opacity", "focused", "color", "badgeHorizontal", "badgeVertical", "StyleSheet", "create", "position", "alignSelf", "alignItems", "justifyContent", "height", "width", "min<PERSON><PERSON><PERSON>", "flex", "marginTop", "left", "top"], "sourceRoot": "../../../src", "sources": ["views/TabBarIcon.tsx"], "mappings": ";;;;;;AACA;AACA;AAQA;AAA4B;AAmBb,SAASA,UAAU,OAWxB;EAAA,IAXyB;IACjCC,KAAK,EAAEC,CAAC;IACRC,UAAU;IACVC,KAAK;IACLC,UAAU;IACVC,aAAa;IACbC,eAAe;IACfC,eAAe;IACfC,iBAAiB;IACjBC,UAAU;IACVC;EACK,CAAC;EACN,MAAMC,IAAI,GAAG,EAAE;;EAEf;EACA;EACA,oBACE,6BAAC,iBAAI;IACH,KAAK,EAAE,CAACT,UAAU,GAAGU,MAAM,CAACC,cAAc,GAAGD,MAAM,CAACE,YAAY,EAAEJ,KAAK;EAAE,gBAEzE,6BAAC,iBAAI;IAAC,KAAK,EAAE,CAACE,MAAM,CAACG,IAAI,EAAE;MAAEC,OAAO,EAAEX;IAAc,CAAC;EAAE,GACpDI,UAAU,CAAC;IACVQ,OAAO,EAAE,IAAI;IACbN,IAAI;IACJO,KAAK,EAAEX;EACT,CAAC,CAAC,CACG,eACP,6BAAC,iBAAI;IAAC,KAAK,EAAE,CAACK,MAAM,CAACG,IAAI,EAAE;MAAEC,OAAO,EAAEV;IAAgB,CAAC;EAAE,GACtDG,UAAU,CAAC;IACVQ,OAAO,EAAE,KAAK;IACdN,IAAI;IACJO,KAAK,EAAEV;EACT,CAAC,CAAC,CACG,eACP,6BAAC,cAAK;IACJ,OAAO,EAAEL,KAAK,IAAI,IAAK;IACvB,KAAK,EAAE,CACLS,MAAM,CAACT,KAAK,EACZD,UAAU,GAAGU,MAAM,CAACO,eAAe,GAAGP,MAAM,CAACQ,aAAa,EAC1DhB,UAAU,CACV;IACF,IAAI,EAAGO,IAAI,GAAG,CAAC,GAAI;EAAE,GAEpBR,KAAK,CACA,CACH;AAEX;AAEA,MAAMS,MAAM,GAAGS,uBAAU,CAACC,MAAM,CAAC;EAC/BP,IAAI,EAAE;IACJ;IACA;IACA;IACAQ,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACb;IACAC,QAAQ,EAAE;EACZ,CAAC;EACDf,YAAY,EAAE;IACZgB,IAAI,EAAE;EACR,CAAC;EACDjB,cAAc,EAAE;IACdc,MAAM,EAAE,MAAM;IACdI,SAAS,EAAE;EACb,CAAC;EACD5B,KAAK,EAAE;IACLoB,QAAQ,EAAE,UAAU;IACpBS,IAAI,EAAE;EACR,CAAC;EACDZ,aAAa,EAAE;IACba,GAAG,EAAE;EACP,CAAC;EACDd,eAAe,EAAE;IACfc,GAAG,EAAE;EACP;AACF,CAAC,CAAC"}
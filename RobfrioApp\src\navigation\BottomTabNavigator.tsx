import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {Text} from 'react-native';

// Screens
import HomeScreen from '../screens/HomeScreen';
import AboutScreen from '../screens/AboutScreen';
import ServicesScreen from '../screens/ServicesScreen';
import ProjectsScreen from '../screens/ProjectsScreen';
import ScheduleScreen from '../screens/ScheduleScreen';
import ContactScreen from '../screens/ContactScreen';

const Tab = createBottomTabNavigator();

const BottomTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({focused, color, size}) => {
          let emoji = '🏠';
          switch (route.name) {
            case 'Home': emoji = '🏠'; break;
            case 'About': emoji = 'ℹ️'; break;
            case 'Services': emoji = '🛠️'; break;
            case 'Projects': emoji = '🖼️'; break;
            case 'Schedule': emoji = '📅'; break;
            case 'Contact': emoji = '📞'; break;
          }
          return <Text style={{fontSize: size}}>{emoji}</Text>;
        },
        tabBarActiveTintColor: '#1e40af',
        tabBarInactiveTintColor: '#64748b',
        tabBarStyle: {
          backgroundColor: '#ffffff',
          borderTopWidth: 1,
          borderTopColor: '#e2e8f0',
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerStyle: {
          backgroundColor: '#1e40af',
          elevation: 4,
          shadowOpacity: 0.3,
        },
        headerTintColor: '#ffffff',
        headerTitleStyle: {
          fontWeight: 'bold',
          fontSize: 18,
        },
        headerTitleAlign: 'center',
      })}>
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: 'Início',
          headerTitle: 'ROBFRIO',
        }}
      />
      <Tab.Screen
        name="About"
        component={AboutScreen}
        options={{
          title: 'Sobre',
          headerTitle: 'Sobre Nós',
        }}
      />
      <Tab.Screen
        name="Services"
        component={ServicesScreen}
        options={{
          title: 'Serviços',
          headerTitle: 'Nossos Serviços',
        }}
      />
      <Tab.Screen
        name="Projects"
        component={ProjectsScreen}
        options={{
          title: 'Projetos',
          headerTitle: 'Galeria de Projetos',
        }}
      />
      <Tab.Screen
        name="Schedule"
        component={ScheduleScreen}
        options={{
          title: 'Agendar',
          headerTitle: 'Solicitar Orçamento',
        }}
      />
      <Tab.Screen
        name="Contact"
        component={ContactScreen}
        options={{
          title: 'Contato',
          headerTitle: 'Entre em Contato',
        }}
      />
    </Tab.Navigator>
  );
};

export default BottomTabNavigator;

var r=require("wonka"),e=require("@urql/core");function t(){return(t=Object.assign||function(r){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n])}return r}).apply(this,arguments)}exports.retryExchange=function(n){var o=n.retryIf,a=n.retryWith,i=n.initialDelayMs||1e3,u=n.maxDelayMs||15e3,y=n.maxNumberAttempts||2,c=n.randomDelay||!0;return function(n){var f=n.forward;return function(n){var l=r.share(n),p=r.makeSubject(),m=p.source,s=p.next,k=r.mergeMap((function(n){var o=n.key,a=n.context,y=(a.retryCount||0)+1,f=a.retryDelay||i,p=Math.random()+1.5;c&&f*p<u&&(f*=p);var m=r.filter((function(r){return("query"===r.kind||"teardown"===r.kind)&&r.key===o}))(l);return r.takeUntil(m)(r.delay(f)(r.fromValue(e.makeOperation(n.kind,n,t({},n.context,{retryDelay:f,retryCount:y})))))}))(m);return r.filter((function(r){if(!r.error||!(o?o(r.error,r.operation):a||r.error.networkError))return!0;if(!((r.operation.context.retryCount||0)>=y-1)){var e=a?a(r.error,r.operation):r.operation;return!e||(s(e),!1)}return!0}))(r.share(f(r.merge([l,k]))))}}};
//# sourceMappingURL=urql-exchange-retry.min.js.map

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';

const ServicesScreen: React.FC = () => {
  const handleWhatsApp = (service: string) => {
    Alert.alert(
      '📱 WhatsApp',
      `Solicitar orçamento para ${service}`,
      [{text: 'OK', onPress: () => {
        const message = `Olá! Gostaria de solicitar um orçamento para ${service}.`;
        const url = `whatsapp://send?phone=5511999999999&text=${encodeURIComponent(message)}`;
        Linking.openURL(url).catch(() => {
          Linking.openURL(`https://wa.me/5511999999999?text=${encodeURIComponent(message)}`);
        });
      }}]
    );
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.headerSection}>
        <Text style={styles.headerTitle}>Nossos Ser<PERSON></Text>
        <Text style={styles.headerSubtitle}>
          Soluções completas em refrigeração comercial
        </Text>
      </View>

      {/* Main Services */}
      <View style={styles.servicesSection}>
        {/* Refrigeração Comercial */}
        <View style={styles.serviceCard}>
          <View style={styles.serviceHeader}>
            <Text style={styles.serviceEmoji}>❄️</Text>
            <Text style={styles.serviceTitle}>Refrigeração Comercial</Text>
          </View>
          
          <View style={styles.serviceContent}>
            <Text style={styles.serviceDescription}>
              Soluções completas para estabelecimentos comerciais com tecnologia de ponta 
              e máxima eficiência energética.
            </Text>
            
            <View style={styles.serviceFeatures}>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>Instalação de sistemas completos</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>Manutenção preventiva e corretiva</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>Modernização de equipamentos</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>Consultoria técnica especializada</Text>
              </View>
            </View>
            
            <TouchableOpacity 
              style={styles.serviceButton}
              onPress={() => handleWhatsApp('Refrigeração Comercial')}>
              <Text style={styles.serviceButtonText}>📱 Solicitar Orçamento</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Câmaras Frigoríficas */}
        <View style={styles.serviceCard}>
          <View style={[styles.serviceHeader, {backgroundColor: '#f97316'}]}>
            <Text style={styles.serviceEmoji}>🏪</Text>
            <Text style={styles.serviceTitle}>Câmaras Frigoríficas</Text>
          </View>
          
          <View style={styles.serviceContent}>
            <Text style={styles.serviceDescription}>
              Projetos personalizados para indústrias e comércios que necessitam de 
              armazenamento refrigerado de alta performance.
            </Text>
            
            <View style={styles.serviceFeatures}>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>Projeto personalizado</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>Montagem completa</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>Isolamento térmico eficiente</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>Sistemas de controle automático</Text>
              </View>
            </View>
            
            <TouchableOpacity 
              style={[styles.serviceButton, {backgroundColor: '#f97316'}]}
              onPress={() => handleWhatsApp('Câmaras Frigoríficas')}>
              <Text style={styles.serviceButtonText}>📱 Solicitar Orçamento</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Additional Services */}
      <View style={styles.additionalServices}>
        <Text style={styles.sectionTitle}>Serviços Adicionais</Text>
        
        <View style={styles.additionalGrid}>
          <View style={styles.additionalCard}>
            <Text style={styles.additionalEmoji}>🔧</Text>
            <Text style={styles.additionalTitle}>Manutenção</Text>
            <Text style={styles.additionalDescription}>
              Preventiva e corretiva com equipe especializada
            </Text>
          </View>
          
          <View style={styles.additionalCard}>
            <Text style={styles.additionalEmoji}>🔄</Text>
            <Text style={styles.additionalTitle}>Modernização</Text>
            <Text style={styles.additionalDescription}>
              Upgrade de sistemas antigos para nova tecnologia
            </Text>
          </View>
          
          <View style={styles.additionalCard}>
            <Text style={styles.additionalEmoji}>💡</Text>
            <Text style={styles.additionalTitle}>Consultoria</Text>
            <Text style={styles.additionalDescription}>
              Análise técnica e recomendações personalizadas
            </Text>
          </View>
          
          <View style={styles.additionalCard}>
            <Text style={styles.additionalEmoji}>🚨</Text>
            <Text style={styles.additionalTitle}>Emergência 24h</Text>
            <Text style={styles.additionalDescription}>
              Atendimento de urgência para situações críticas
            </Text>
          </View>
        </View>
      </View>

      {/* Contact CTA */}
      <View style={styles.ctaSection}>
        <Text style={styles.ctaTitle}>Pronto para começar seu projeto?</Text>
        <Text style={styles.ctaDescription}>
          Entre em contato conosco e receba um orçamento personalizado
        </Text>
        <TouchableOpacity 
          style={styles.ctaButton}
          onPress={() => handleWhatsApp('informações sobre serviços')}>
          <Text style={styles.ctaButtonText}>💬 Falar com Especialista</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  headerSection: {
    backgroundColor: '#1e40af',
    paddingHorizontal: 20,
    paddingVertical: 30,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#e2e8f0',
    textAlign: 'center',
  },
  servicesSection: {
    padding: 20,
    gap: 20,
  },
  serviceCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  serviceHeader: {
    backgroundColor: '#1e40af',
    padding: 24,
    alignItems: 'center',
  },
  serviceEmoji: {
    fontSize: 40,
    marginBottom: 12,
  },
  serviceTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  serviceContent: {
    padding: 20,
  },
  serviceDescription: {
    fontSize: 16,
    color: '#64748b',
    lineHeight: 24,
    marginBottom: 20,
    textAlign: 'center',
  },
  serviceFeatures: {
    gap: 12,
    marginBottom: 24,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureIcon: {
    fontSize: 16,
  },
  featureText: {
    fontSize: 14,
    color: '#64748b',
    flex: 1,
  },
  serviceButton: {
    backgroundColor: '#1e40af',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  serviceButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  additionalServices: {
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 20,
    textAlign: 'center',
  },
  additionalGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  additionalCard: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    width: '47%',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  additionalEmoji: {
    fontSize: 32,
    marginBottom: 8,
  },
  additionalTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 4,
  },
  additionalDescription: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 16,
  },
  ctaSection: {
    padding: 20,
    alignItems: 'center',
  },
  ctaTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 8,
    textAlign: 'center',
  },
  ctaDescription: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  ctaButton: {
    backgroundColor: '#25d366',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 25,
  },
  ctaButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ServicesScreen;

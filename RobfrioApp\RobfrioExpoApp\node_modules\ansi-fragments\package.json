{"name": "ansi-fragments", "version": "0.2.1", "main": "build/index.js", "license": "MIT", "description": "A tiny library with builders to help making logs/CLI pretty with a nice DX.", "repository": {"type": "git", "url": "https://github.com/zamotany/ansi-fragments.git"}, "keywords": ["cli", "ansi"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/zamotany/ansi-fragments/issues"}, "homepage": "https://github.com/zamotany/ansi-fragments", "files": ["build/"], "scripts": {"lint": "tslint -t stylish --project tsconfig.json", "test": "jest", "build": "tsc --build tsconfig.json", "clean": "del build", "prepare": "yarn clean && yarn build"}, "dependencies": {"colorette": "^1.0.7", "slice-ansi": "^2.0.0", "strip-ansi": "^5.0.0"}, "devDependencies": {"@callstack/tslint-config": "^0.1.0", "@types/jest": "^23.3.13", "@types/slice-ansi": "^2.0.0", "@types/strip-ansi": "^3.0.0", "del-cli": "^1.1.0", "jest": "^24.0.0", "ts-jest": "^23.10.5", "tslint": "^5.12.0", "typescript": "^3.2.2"}, "jest": {"preset": "ts-jest", "rootDir": "./src"}}